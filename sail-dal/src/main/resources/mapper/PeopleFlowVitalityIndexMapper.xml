<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.PeopleFlowVitalityIndexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.PeopleFlowVitalityIndexDO">
        <id column="id" property="id"/>
        <result column="area_code" property="areaCode"/>
        <result column="sea_area_code" property="seaAreaCode"/>
        <result column="time" property="time"/>
        <result column="batch_id" property="batchId"/>
        <result column="activity_index" property="activityIndex"/>
        <result column="flow" property="flow"/>
        <result column="pre_flow" property="preFlow"/>
        <result column="today_cumulative_flow" property="todayCumulativeFlow"/>
        <result column="avg_flow" property="avgFlow"/>
        <result column="max_flow" property="maxFlow"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, area_code, sea_area_code, time, batch_id, activity_index, flow, pre_flow,
        today_cumulative_flow, avg_flow, max_flow, tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

</mapper>