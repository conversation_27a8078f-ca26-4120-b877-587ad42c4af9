<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.SailRouteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.SailRouteDO">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="wkt" property="wkt"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="length" property="length"/>
        <result column="type" property="type"/>
        <result column="source" property="source"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, tenant_id, wkt, is_deleted, length, type, source, gmt_created, gmt_modified
    </sql>

</mapper>
