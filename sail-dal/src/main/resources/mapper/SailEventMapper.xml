<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.SailEventMapper">

    <!-- 事件类型统计结果映射 -->
    <resultMap id="EventTypeStatisticsResultMap" type="com.deepinnet.sail.dal.dto.EventTypeStatisticsDTO">
        <result column="event_source" property="eventSource"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 统计各事件类型的数量 -->
    <select id="getEventTypeStatistics" resultMap="EventTypeStatisticsResultMap">
        SELECT 
            event_source,
            COUNT(*) as count
        FROM sail_event
        WHERE is_deleted = false
        <if test="startTime != null">
            AND incident_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND incident_time &lt;= #{endTime}
        </if>
        GROUP BY event_source
        ORDER BY count DESC
    </select>

</mapper>