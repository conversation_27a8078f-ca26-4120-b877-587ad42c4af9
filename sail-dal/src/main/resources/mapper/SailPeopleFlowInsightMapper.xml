<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.SailPeopleFlowInsightMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO">
        <id column="id" property="id"/>
        <result column="value" property="value"/>
        <result column="count" property="count"/>
        <result column="profile" property="profile"/>
        <result column="request_time" property="requestTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, value, count, profile, request_time, gmt_created, gmt_modified
    </sql>

    <!-- 批量插入人流洞察数据 -->
    <insert id="batchInsert">
        INSERT INTO sail_people_flow_insight (value, count, profile, request_time, gmt_created, gmt_modified)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.value}, #{item.count}, #{item.profile}, #{item.requestTime}, #{item.gmtCreated}, #{item.gmtModified})
        </foreach>
    </insert>

</mapper>