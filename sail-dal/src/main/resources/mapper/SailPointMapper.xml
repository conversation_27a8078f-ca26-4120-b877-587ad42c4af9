<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.SailPointMapper">

    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.SailPointDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="wkt" column="wkt" jdbcType="OTHER" typeHandler="com.deepinnet.sail.dal.typehandler.PGPointTypeHandler"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,name,tenant_id,wkt,is_deleted,address,type,source,gmt_created,gmt_modified
    </sql>

</mapper>

