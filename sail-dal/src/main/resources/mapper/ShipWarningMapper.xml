<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipWarningDO">
        <id column="id" property="id"/>
        <result column="ship_no" property="shipNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="warning_type" property="warningType"/>
        <result column="description" property="description"/>
        <result column="x" property="x"/>
        <result column="y" property="y"/>
        <result column="trigger_time" property="triggerTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ship_no, ship_name, warning_type, status, description, x, y,
        trigger_time, close_time, close_reason, ext_info, gmt_created, gmt_modified
    </sql>

    <!-- 分页查询船舶预警记录（联表查询） -->
    <select id="selectShipWarningsWithShipInfo" resultType="com.deepinnet.sail.dal.dto.ShipWarningDTO">
        SELECT
            sw.id,
            sw.ship_no AS shipNo,
            sw.ship_name AS shipName,
            sw.warning_type AS warningType,
            sw.description,
            sw.trigger_time AS triggerTime,
            sw.x,
            sw.y,
            sw.sailing_record_id AS sailingRecordId,
            COALESCE(s.owner, '') AS owner,
            COALESCE(s.owner_phone, '') AS ownerPhone,
            COALESCE(s.port_code, '') AS portCode,
            COALESCE(s.port_name, '') AS portName,
            COALESCE(s.management_company, '') AS managementCompany,
            COALESCE(s.responsible_person, '') AS responsiblePerson,
            COALESCE(s.responsible_person_phone, '') AS responsiblePersonPhone,
            COALESCE(s.office, '') AS office
        FROM ship_warning sw
        LEFT JOIN ship s ON sw.ship_no = s.ship_no
        <where>
            sw.is_deleted = false
            <if test="query.shipName != null and query.shipName != ''">
                AND sw.ship_name LIKE CONCAT('%', #{query.shipName}, '%')
            </if>
            <if test="query.portCode != null and query.portCode != ''">
                AND s.port LIKE CONCAT('%', #{query.portCode}, '%')
            </if>
            <if test="query.owner != null and query.owner != ''">
                AND s.owner LIKE CONCAT('%', #{query.owner}, '%')
            </if>
            <if test="query.ownerPhone != null and query.ownerPhone != ''">
                AND s.owner_phone LIKE CONCAT('%', #{query.ownerPhone}, '%')
            </if>
            <if test="query.warningType != null and query.warningType != ''">
                AND sw.warning_type = #{query.warningType}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND sw.trigger_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND sw.trigger_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY sw.trigger_time DESC
    </select>

    <!-- 预警类型统计结果映射 -->
    <resultMap id="WarningTypeStatisticsResultMap" type="com.deepinnet.sail.dal.dto.WarningTypeStatisticsDTO">
        <result column="warning_type" property="warningType"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 统计各预警类型的数量 -->
    <select id="getWarningTypeStatistics" resultMap="WarningTypeStatisticsResultMap">
        SELECT
            warning_type,
            COUNT(*) as count
        FROM ship_warning
        WHERE is_deleted = false
        <if test="startTime != null">
            AND trigger_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND trigger_time &lt;= #{endTime}
        </if>
        GROUP BY warning_type
        ORDER BY count DESC
    </select>

</mapper>
