<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipPositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipPositionDO">
        <id column="id" property="id"/>
        <result column="ship_no" property="shipNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="x" property="x"/>
        <result column="y" property="y"/>
        <result column="report_time" property="reportTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="port_code" property="portCode"/>
        <result column="port_name" property="portName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , ship_no, ship_name, x, y, report_time, gmt_created, gmt_modified, tenant_id, port_code, port_name
    </sql>

    <!-- 获取指定时间范围内每个船舶的最新位置数据 -->
    <select id="getLatestPositionsByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM (
            SELECT
                <include refid="Base_Column_List"/>,
                ROW_NUMBER() OVER (PARTITION BY ship_no ORDER BY report_time DESC) as rn
            FROM ship_position
            WHERE report_time >= #{startTime}
        ) ranked
        WHERE rn = 1
        ORDER BY report_time DESC
    </select>

    <!-- 查询船舶实时位置（1小时内数据，使用EXISTS逻辑） -->
    <select id="getLatestPositionsByShipNos" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ship_position sp1
        WHERE sp1.report_time >= #{oneHourAgo}
        <if test="shipNos != null and shipNos.size() > 0">
            AND sp1.ship_no IN
            <foreach collection="shipNos" item="shipNo" open="(" separator="," close=")">
                #{shipNo}
            </foreach>
        </if>
        AND NOT EXISTS (
        SELECT 1 FROM ship_position sp2
        WHERE sp2.ship_no = sp1.ship_no
        AND sp2.report_time > sp1.report_time
        AND sp2.report_time >= #{oneHourAgo}
        )
        ORDER BY sp1.report_time DESC
    </select>

</mapper>
