<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.SeaAreaFlowSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.SeaAreaFlowSummaryDO">
        <id column="id" property="id"/>
        <result column="sea_area_code" property="seaAreaCode"/>
        <result column="sea_area_name" property="seaAreaName"/>
        <result column="summary_date" property="summaryDate"/>
        <result column="today_total_flow" property="todayTotalFlow"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sea_area_code, sea_area_name, summary_date, today_total_flow,
        tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

</mapper>