<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipTimeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipTimeConfigDO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="config_type" property="configType"/>
        <result column="config_value" property="configValue"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , tenant_id, config_type, config_value, is_enabled, gmt_created, gmt_modified
    </sql>

</mapper>
