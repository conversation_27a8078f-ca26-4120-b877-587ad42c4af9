package com.deepinnet.sail.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人流洞察数据 Mapper 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Mapper
public interface SailPeopleFlowInsightMapper extends BaseMapper<SailPeopleFlowInsightDO> {

    /**
     * 批量插入人流洞察数据
     *
     * @param dataList 数据列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("dataList") List<SailPeopleFlowInsightDO> dataList);
}