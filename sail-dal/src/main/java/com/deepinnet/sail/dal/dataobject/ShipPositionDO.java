package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 船舶位置信息表
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@Setter
@TableName("ship_position")
public class ShipPositionDO extends Model<ShipPositionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 船舶编号
     */
    @TableField("ship_no")
    private String shipNo;

    /**
     * 船舶名称
     */
    @TableField("ship_name")
    private String shipName;

    /**
     * 经度
     */
    @TableField("x")
    private String x;

    /**
     * 纬度
     */
    @TableField("y")
    private String y;

    /**
     * 上报时间（时间戳）
     */
    @TableField("report_time")
    private Long reportTime;

    /**
     * 船舶关联的港口
     */
    @TableField("port_code")
    private String portCode;

    @TableField("port_name")
    private String portName;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
