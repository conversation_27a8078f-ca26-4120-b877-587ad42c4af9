package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.*;

import java.sql.*;
import java.util.*;

public class StringListTypeHandler extends BaseTypeHandler<List<String>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.OTHER);
            return;
        }
        // Convert the List<String> to a PostgreSQL array
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf("varchar", parameter.toArray());
        ps.setArray(i, array);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return convertArrayToList(array);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return convertArrayToList(array);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return convertArrayToList(array);
    }

    private List<String> convertArrayToList(Array sqlArray) throws SQLException {
        if (sqlArray == null) {
            return null;
        }
        String[] stringArray = (String[]) sqlArray.getArray();
        return new ArrayList<>(Arrays.asList(stringArray));
    }
}
