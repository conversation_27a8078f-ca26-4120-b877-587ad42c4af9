package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 船舶时间配置表
 *
 * @TableName ship_time_config
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@TableName(value = "ship_time_config")
@Data
public class ShipTimeConfigDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 配置类型：daily_return_time-最迟归港时间，regular_risk_time-常规风险时段，custom_risk_time-自定义风险时段
     */
    private String configType;

    /**
     * 配置值JSON格式
     */
    private String configValue;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
