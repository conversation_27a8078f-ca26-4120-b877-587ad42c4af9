package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.LineString;
import org.postgis.PGgeometry;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2024-04-25
 **/

@MappedTypes({LineString.class})
public class PGLineStringTypeHandler extends AbstractGeometryTypeHandler<LineString> {

    public LineString getResult(PGgeometry pGgeometry) {
        if (pGgeometry == null) {
            return null;
        }
        String pgWkt = pGgeometry.toString();
        String target = String.format("SRID=%s;", AbstractGeometryTypeHandler.SRID_IN_DB);
        String wkt = pgWkt.replace(target, "");
        try {
            return (LineString) AbstractGeometryTypeHandler.READER_POOL.get().read(wkt);
        } catch (Exception e) {
            throw new RuntimeException("解析wkt失败：" + wkt, e);
        }
    }
}
