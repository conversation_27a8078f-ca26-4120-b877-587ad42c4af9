package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.MultiPolygon;
import org.postgis.PGgeometry;

/**
 * <PERSON><PERSON>
 * Date 2024-04-25
 **/

@MappedTypes({MultiPolygon.class})
public class PGMultiPolygonTypeHandler extends AbstractGeometryTypeHandler<MultiPolygon> {

    public MultiPolygon getResult(PGgeometry pGgeometry) {
        if (pGgeometry == null) {
            return null;
        }
        String pgWkt = pGgeometry.toString();
        String target = String.format("SRID=%s;", AbstractGeometryTypeHandler.SRID_IN_DB);
        String wkt = pgWkt.replace(target, "");
        try {
            return (MultiPolygon) AbstractGeometryTypeHandler.READER_POOL.get().read(wkt);
        } catch (Exception e) {
            throw new RuntimeException("解析Polygon wkt失败：" + wkt, e);
        }
    }
}
