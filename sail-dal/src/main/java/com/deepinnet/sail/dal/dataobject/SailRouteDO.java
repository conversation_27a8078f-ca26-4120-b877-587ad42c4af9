package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.deepinnet.sail.dal.typehandler.PGLineStringTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.LineString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线路表
 *
 * @TableName sail_route
 * <AUTHOR>
 * @since 2025-01-15
 */
@TableName(value = "sail_route")
@Data
public class SailRouteDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 线路编码
     */
    private String code;

    /**
     * 线路名称
     */
    private String name;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 线路的wkt几何信息
     */
    @TableField(typeHandler = PGLineStringTypeHandler.class)
    private LineString wkt;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 线路长度
     */
    private String length;

    /**
     * 线路类型分类
     */
    private String type;

    /**
     * 来源：userDefined-自建线路，gaoDe-高德同步的线路
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
