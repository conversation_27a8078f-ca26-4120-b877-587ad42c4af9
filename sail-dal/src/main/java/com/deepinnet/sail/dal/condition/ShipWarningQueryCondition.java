package com.deepinnet.sail.dal.condition;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wong
 * @create 2025/8/22 17:22
 * @Description
 *
 */
@Data
public class ShipWarningQueryCondition {
    private String shipName;

    private String portCode;

    private String owner;

    private String warningType;

    private String ownerPhone;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
}
