package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 事件实体类
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@TableName("sail_event")
public class SailEventDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件编号
     */
    @TableField("event_no")
    private String eventNo;

    /**
     * 事件来源
     */
    @TableField("event_source")
    private String eventSource;

    /**
     * 事件描述
     */
    @TableField("event_description")
    private String eventDescription;

    /**
     * 紧急程度
     */
    @TableField("urgency_level")
    private String urgencyLevel;

    /**
     * 事发地址
     */
    @TableField("incident_address")
    private String incidentAddress;

    /**
     * 经度
     */
    @TableField("x")
    private String x;

    /**
     * 纬度
     */
    @TableField("y")
    private String y;

    /**
     * 事发时间
     */
    @TableField("incident_time")
    private LocalDateTime incidentTime;

    /**
     * 上报部门
     */
    @TableField("report_department")
    private String reportDepartment;

    /**
     * 上报人名称
     */
    @TableField("reporter_name")
    private String reporterName;

    /**
     * 上报人手机号
     */
    @TableField("reporter_phone")
    private String reporterPhone;

    /**
     * 上报时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 事件状态
     */
    @TableField("status")
    private String status;

    /**
     * 附件信息（JSON格式存储图片/视频路径）
     */
    @TableField("attachments")
    private String attachments;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;
}
