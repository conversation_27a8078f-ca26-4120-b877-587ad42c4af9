package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Point;
import org.postgis.PGgeometry;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2024-04-25
 **/
@MappedTypes({Point.class})
public class PGPointTypeHandler extends AbstractGeometryTypeHandler<Point> {

    public Point getResult(PGgeometry pGgeometry) {
        if (pGgeometry == null) {
            return null;
        }
        String pgWkt = pGgeometry.toString();
        String target = String.format("SRID=%s;", SRID_IN_DB);
        String wkt = pgWkt.replace(target, "");
        try {
            return (Point) READER_POOL.get().read(wkt);
        } catch (Exception e) {
            throw new RuntimeException("解析wkt失败：" + wkt, e);
        }
    }
}
