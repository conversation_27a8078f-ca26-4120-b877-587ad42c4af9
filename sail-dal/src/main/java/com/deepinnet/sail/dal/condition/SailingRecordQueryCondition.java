package com.deepinnet.sail.dal.condition;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wong
 * @create 2025/8/19 17:54
 * @Description
 *
 */
@Data
public class SailingRecordQueryCondition {

    private String shipName;

    private String portName;

    private LocalDateTime departureStartDate;

    private LocalDateTime departureEndDate;

    private String returnStatus;

    private String warningType;
}
