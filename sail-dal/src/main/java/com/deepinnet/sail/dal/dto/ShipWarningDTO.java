package com.deepinnet.sail.dal.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wong
 * @create 2025/8/22 17:21
 * @Description
 *
 */
@Data
public class ShipWarningDTO {

    private Long id;

    /**
     * 船舶编号
     */
    private String shipNo;

    /**
     * 船舶名称
     */
    private String shipName;

    private String warningType;

    private String portCode;

    private String portName;

    /**
     * 预警描述
     */
    private String description;

    /**
     * 触发时的经度
     */
    private String x;

    /**
     * 触发时的纬度
     */
    private String y;

    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;

    /**
     * 出海记录ID
     */
    private Long sailingRecordId;

    /**
     * 船舶所有人
     */
    private String owner;

    /**
     * 船主联系电话
     */
    private String ownerPhone;

    /**
     * 纳管公司
     */
    private String managementCompany;

    /**
     * 包干责任人
     */
    private String responsiblePerson;

    /**
     * 责任人联系电话
     */
    private String responsiblePersonPhone;

    /**
     * 所属办事处
     */
    private String office;
}
