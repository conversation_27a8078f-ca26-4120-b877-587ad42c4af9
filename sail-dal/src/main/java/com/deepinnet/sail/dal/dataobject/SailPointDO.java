package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.deepinnet.sail.dal.typehandler.PGPointTypeHandler;
import lombok.Data;
import org.locationtech.jts.geom.Point;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点位表
 *
 * @TableName sail_point
 * <AUTHOR>
 * @since 2025-01-15
 */
@TableName(value = "sail_point")
@Data
public class SailPointDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 点位编码
     */
    private String code;

    /**
     * 点位名称
     */
    private String name;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 点位的wkt几何信息
     */
    @TableField(typeHandler = PGPointTypeHandler.class)
    private Point wkt;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 点位地址
     */
    private String address;

    /**
     * 点位类型分类
     */
    private String type;

    /**
     * 来源：userDefined-自建点位，gaoDe-高德同步的点位
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

