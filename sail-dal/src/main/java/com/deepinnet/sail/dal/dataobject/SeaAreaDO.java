package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 海域表
 *
 * @TableName sea_area
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@TableName(value = "sea_area")
@Data
public class SeaAreaDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 海域code
     */
    private String code;

    /**
     * 区域code
     */
    private String areaCode;

    private String adCode;

    /**
     * 海域名称
     */
    private String name;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 海域的wkt
     */
    private String wkt;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 面积
     */
    private String area;

    /**
     * 类型：forbidden-禁海区，normal-普通海域、port-港口
     */
    private String type;

    /**
     * 来源：userDefined-自建海域，gaoDe-高德同步的海域
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
