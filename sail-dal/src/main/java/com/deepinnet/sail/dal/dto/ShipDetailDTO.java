package com.deepinnet.sail.dal.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 船舶详细信息DTO（含位置）
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
public class ShipDetailDTO {

    /**
     * 船舶ID
     */
    private Long id;

    /**
     * 船舶编号
     */
    private String shipNo;

    /**
     * 船舶名称
     */
    private String shipName;

    /**
     * 纳管公司
     */
    private String managementCompany;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 是否纳管
     */
    private Boolean isManaged;

    /**
     * 船舶类型
     */
    private String shipType;

    /**
     * 船主联系电话
     */
    private String ownerPhone;

    /**
     * 自用船舶是否签署承诺书
     */
    private Boolean isCommitmentSigned;

    /**
     * 是否备案
     */
    private Boolean isFiled;

    /**
     * 包干责任人
     */
    private String responsiblePerson;

    /**
     * 责任人联系电话
     */
    private String responsiblePersonPhone;

    /**
     * 港口编号
     */
    private String portCode;

    /**
     * 所属港口
     */
    private String portName;

    /**
     * 所属办事处
     */
    private String office;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}