package com.deepinnet.sail.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.sail.dal.dataobject.SailEventDO;
import com.deepinnet.sail.dal.dto.EventTypeStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件Mapper
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Mapper
public interface SailEventMapper extends BaseMapper<SailEventDO> {

    /**
     * 统计各事件类型的数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 事件类型统计列表
     */
    List<EventTypeStatisticsDTO> getEventTypeStatistics(@Param("startTime") LocalDateTime startTime, 
                                                        @Param("endTime") LocalDateTime endTime);
}
