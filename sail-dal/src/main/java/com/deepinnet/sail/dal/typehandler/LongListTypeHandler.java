package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.*;

import java.sql.*;
import java.util.*;

public class LongListTypeHandler extends BaseTypeHandler<List<Long>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.OTHER);
            return;
        }
        // Convert the List<Long> to a PostgreSQL array
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf("bigint", parameter.toArray());
        ps.setArray(i, array);
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return convertArrayToList(array);
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return convertArrayToList(array);
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return convertArrayToList(array);
    }

    private List<Long> convertArrayToList(Array sqlArray) throws SQLException {
        if (sqlArray == null) {
            return null;
        }
        Long[] stringArray = (Long[]) sqlArray.getArray();
        return new ArrayList<>(Arrays.asList(stringArray));
    }
}
