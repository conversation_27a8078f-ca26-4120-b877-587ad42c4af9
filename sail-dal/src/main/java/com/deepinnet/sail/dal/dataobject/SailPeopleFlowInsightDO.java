package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 人流洞察数据对象
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Data
@TableName("sail_people_flow_insight")
public class SailPeopleFlowInsightDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 值
     */
    private String val;

    /**
     * 数量
     */
    private Long count;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 洞察维度
     */
    private String profile;

    /**
     * 请求时间（时间戳）
     */
    private Long requestTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}