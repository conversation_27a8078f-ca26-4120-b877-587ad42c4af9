package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 事件处理记录实体类
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@TableName("sail_event_process")
public class SailEventProcessDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件ID
     */
    @TableField("event_no")
    private String eventNo;

    /**
     * 处理人工号
     */
    @TableField("processor_no")
    private String processorNo;

    /**
     * 处理人姓名
     */
    @TableField("processor_name")
    private String processorName;

    /**
     * 处理部门
     */
    @TableField("process_department")
    private Long processDepartment;

    /**
     * 处理阶段
     */
    @TableField("process_stage")
    private String processStage;

    /**
     * 处理意见
     */
    @TableField("process_opinion")
    private String processOpinion;

    /**
     * 处理时间
     */
    @TableField("process_time")
    private LocalDateTime processTime;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;
}
