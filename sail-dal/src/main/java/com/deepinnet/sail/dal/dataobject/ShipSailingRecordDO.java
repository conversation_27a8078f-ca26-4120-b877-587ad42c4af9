package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 船舶出海记录表
 *
 * <AUTHOR> wong
 * @TableName ship_sailing_record
 * @since 2025-08-19
 */
@TableName(value = "ship_sailing_record")
@Data
public class ShipSailingRecordDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 船舶编号
     */
    private String shipNo;

    /**
     * 船舶名称
     */
    private String shipName;

    /**
     * 船舶关联的港口名称
     */
    private String portCode;

    /**
     * 船舶关联的港口名称
     */
    private String portName;

    /**
     * 出港时间
     */
    private LocalDateTime departureTime;

    /**
     * 归港时间
     */
    private LocalDateTime returnTime;

    /**
     * 归港状态：SAILING-未归，RETURNED-已归
     */
    private String returnStatus;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
