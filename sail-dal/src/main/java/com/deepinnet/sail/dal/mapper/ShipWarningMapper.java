package com.deepinnet.sail.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.sail.dal.condition.ShipWarningQueryCondition;
import com.deepinnet.sail.dal.dataobject.ShipWarningDO;
import com.deepinnet.sail.dal.dto.ShipWarningDTO;
import com.deepinnet.sail.dal.dto.WarningTypeStatisticsDTO;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 船舶预警表 Mapper 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
@Mapper
public interface ShipWarningMapper extends BaseMapper<ShipWarningDO> {

    /**
     * 分页查询船舶预警记录（联表查询）
     *
     * @param queryCondition 查询条件
     * @return 预警记录列表
     */
    List<ShipWarningDTO> selectShipWarningsWithShipInfo(@Param("query") ShipWarningQueryCondition queryCondition);

    /**
     * 统计各预警类型的数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预警类型统计列表
     */
    List<WarningTypeStatisticsDTO> getWarningTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);
}
