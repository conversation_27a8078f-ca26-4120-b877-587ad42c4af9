package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wong
 * @create 2025/8/29 10:36
 * @Description
 *
 */
@Data
@TableName("people_flow_vitality_index")
public class PeopleFlowVitalityIndexDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 海域code
     */
    private String seaAreaCode;

    /**
     * 时间戳
     */
    private Long time;

    /**
     * 批次号
     */
    private Long batchId;

    /**
     * ⼈流活⼒指数
     */
    private String activityIndex;

    /**
     * ⼈流量(当前批次)
     */
    private String flow;

    /**
     * 上⼀个批次⼈流量
     */
    private String preFlow;

    /**
     * 今⽇累计⼈流量
     */
    private String todayCumulativeFlow;

    /**
     * 历史平均⼈流量（结合历史星期和节假⽇特征）
     */
    private String avgFlow;

    /**
     * 历史最⼤⼈流量
     */
    private String maxFlow;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 租户id
     */
    private String tenantId;

    private Boolean isDeleted;
}
