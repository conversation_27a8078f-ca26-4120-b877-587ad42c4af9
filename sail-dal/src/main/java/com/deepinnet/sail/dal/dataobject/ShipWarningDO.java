package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 船舶预警表
 *
 * @TableName ship_warning
 * <AUTHOR> wong
 * @since 2025-08-18
 */
@TableName(value = "ship_warning")
@Data
public class ShipWarningDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 船舶编号
     */
    private String shipNo;

    /**
     * 船舶名称
     */
    private String shipName;

    /**
     * 出海记录ID（关联字段）
     */
    private Long sailingRecordId;

    /**
     * 预警类型：RISK_TIME_SAILING-风险时段出海，SEA_AREA_DEVIATION-海域偏离，
     * FORBIDDEN_AREA-前往禁海区，OVERDUE_RETURN-到时未归，DAILY_NOT_RETURN-当日未归，
     * REMOTE_NOT_RETURN-异地未归
     */
    private String warningType;

    /**
     * 预警描述
     */
    private String description;

    /**
     * 触发时的经度
     */
    private String x;

    /**
     * 触发时的纬度
     */
    private String y;

    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
