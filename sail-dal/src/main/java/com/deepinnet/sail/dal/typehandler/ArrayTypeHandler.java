package com.deepinnet.sail.dal.typehandler;

import org.apache.ibatis.type.*;

import java.sql.*;
import java.util.Arrays;

/**
 * PostgreSQL数组类型处理器
 *
 * <AUTHOR>
 */
@MappedTypes({String[].class})
@MappedJdbcTypes({JdbcType.ARRAY, JdbcType.VARCHAR})
public class ArrayTypeHandler extends BaseTypeHandler<String[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String[] parameter, JdbcType jdbcType) throws SQLException {
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf("text", parameter);
        ps.setArray(i, array);
    }

    @Override
    public String[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return convertArray(array);
    }

    @Override
    public String[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return convertArray(array);
    }

    @Override
    public String[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return convertArray(array);
    }

    private String[] convertArray(Array array) throws SQLException {
        if (array == null) {
            return null;
        }
        Object obj = array.getArray();
        if (obj instanceof String[]) {
            return (String[]) obj;
        } else if (obj instanceof Object[]) {
            Object[] objArray = (Object[]) obj;
            return Arrays.stream(objArray)
                    .map(Object::toString)
                    .toArray(String[]::new);
        }
        return null;
    }
} 