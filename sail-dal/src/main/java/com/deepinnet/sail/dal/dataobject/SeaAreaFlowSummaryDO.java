package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.*;

/**
 * 海域每日人流统计表
 *
 * <AUTHOR> wong
 * @TableName sea_area_daily_flow_statistics
 * @since 2025-08-29
 */
@Data
@TableName("sea_area_flow_summary")
public class SeaAreaFlowSummaryDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 海域code
     */
    private String seaAreaCode;

    /**
     * 海域名称
     */
    private String seaAreaName;

    /**
     * 统计日期
     */
    private LocalDateTime summaryDate;

    /**
     * 当日累计人流量
     */
    private String todayTotalFlow;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}