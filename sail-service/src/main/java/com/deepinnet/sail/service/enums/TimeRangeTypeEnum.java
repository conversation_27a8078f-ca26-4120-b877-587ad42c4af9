package com.deepinnet.sail.service.enums;

import lombok.Getter;

/**
 * 时间区间类型枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-20
 */
@Getter
public enum TimeRangeTypeEnum {

    /**
     * 天
     */
    DAY("day", "天"),

    /**
     * 月份
     */
    MONTH("month", "月");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    TimeRangeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static TimeRangeTypeEnum getByCode(String code) {
        for (TimeRangeTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为日级别的时间范围（需要按小时统计）
     *
     * @return true-日级别，false-月级别
     */
    public boolean isDayLevel() {
        return this == DAY;
    }

    /**
     * 判断是否为月级别的时间范围（需要按天统计）
     *
     * @return true-月级别，false-日级别
     */
    public boolean isMonthLevel() {
        return this == MONTH;
    }
}
