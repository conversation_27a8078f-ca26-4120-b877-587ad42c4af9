package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO;
import com.deepinnet.sail.dal.mapper.SailPeopleFlowInsightMapper;
import com.deepinnet.sail.service.repository.SailPeopleFlowInsightRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人流洞察数据仓储实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Repository
public class SailPeopleFlowInsightRepositoryImpl extends ServiceImpl<SailPeopleFlowInsightMapper, SailPeopleFlowInsightDO> 
        implements SailPeopleFlowInsightRepository {

    @Override
    public boolean batchSave(List<SailPeopleFlowInsightDO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return true;
        }
        
        // 设置创建和修改时间
        LocalDateTime now = LocalDateTime.now();
        dataList.forEach(data -> {
            data.setGmtCreated(now);
            data.setGmtModified(now);
        });
        
        int result = baseMapper.batchInsert(dataList);
        return result > 0;
    }
}