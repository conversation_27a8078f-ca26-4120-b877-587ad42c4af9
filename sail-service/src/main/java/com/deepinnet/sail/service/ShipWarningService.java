package com.deepinnet.sail.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;

/**
 * 船舶预警记录服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-22
 */
public interface ShipWarningService {

    /**
     * 分页查询船舶预警记录
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<ShipWarningVO> pageShipWarnings(ShipWarningQueryDTO queryDTO);

    /**
     * 获取大屏预警总览数据
     *
     * @param queryDTO 查询条件
     * @return 预警总览数据
     */
    WarningDashboardSummaryVO getWarningDashboardSummary(WarningDashboardSummaryQueryDTO queryDTO);
}
