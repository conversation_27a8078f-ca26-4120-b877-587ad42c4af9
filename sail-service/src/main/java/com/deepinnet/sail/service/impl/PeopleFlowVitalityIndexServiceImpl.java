package com.deepinnet.sail.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.sail.dal.dataobject.PeopleFlowVitalityIndexDO;
import com.deepinnet.sail.service.PeopleFlowVitalityIndexService;
import com.deepinnet.sail.service.convert.PeopleFlowVitalityIndexConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.repository.PeopleFlowVitalityIndexRepository;
import com.deepinnet.sail.service.vo.PeopleFlowVitalityIndexVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 人流活力指数服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Service
@RequiredArgsConstructor
public class PeopleFlowVitalityIndexServiceImpl implements PeopleFlowVitalityIndexService {

    private final PeopleFlowVitalityIndexRepository peopleFlowVitalityIndexRepository;
    private final PeopleFlowVitalityIndexConvert peopleFlowVitalityIndexConvert;

    @Override
    public PeopleFlowVitalityIndexVO getLatestByAreaCode(PeopleFlowVitalityIndexQueryDTO queryDTO) {
        // 查询最新批次的人流活力指数数据
        List<PeopleFlowVitalityIndexDO> dataList = peopleFlowVitalityIndexRepository.findLatestByAreaCode(queryDTO.getAreaCode());

        if (CollUtil.isEmpty(dataList)) {
            LogUtil.info("未查询到区域{}的人流活力指数数据", queryDTO.getAreaCode());
            return null;
        }

        // 转换为VO
        return buildPeopleFlowVitalityIndexVO(dataList);
    }

    private PeopleFlowVitalityIndexVO buildPeopleFlowVitalityIndexVO(List<PeopleFlowVitalityIndexDO> dataList) {
        PeopleFlowVitalityIndexVO vitalityIndexVO = new PeopleFlowVitalityIndexVO();
        List<PeopleFlowVitalityIndexDTO> indexDTOList = peopleFlowVitalityIndexConvert.toDTOList(dataList);
        vitalityIndexVO.setPeopleFlowVitalityIndexDTOs(indexDTOList);

        int totalFlow = 0;
        for (PeopleFlowVitalityIndexDTO vitalityIndexDTO : indexDTOList) {
            totalFlow += Integer.parseInt(vitalityIndexDTO.getTodayCumulativeFlow());
        }

        vitalityIndexVO.setTotalFlow(totalFlow);

        return vitalityIndexVO;
    }
}