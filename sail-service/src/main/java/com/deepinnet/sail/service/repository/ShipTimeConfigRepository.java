package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.ShipTimeConfigDO;

/**
 * 船舶时间配置表 Repository 接口
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
public interface ShipTimeConfigRepository extends IService<ShipTimeConfigDO> {

    /**
     * 根据租户ID和配置类型查询配置
     *
     * @param configType 配置类型
     * @return 配置信息
     */
    ShipTimeConfigDO findByTenantIdAndConfigType(String configType);
}
