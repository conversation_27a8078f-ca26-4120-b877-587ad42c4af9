package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.ShipDO;
import com.deepinnet.sail.service.vo.ShipDetailVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 船舶转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Mapper(componentModel = "spring")
public interface ShipConvert {

    ShipDetailVO toDetailVO(ShipDO shipDO);

    List<ShipDetailVO> toDetailVOList(List<ShipDO> shipDOList);
}