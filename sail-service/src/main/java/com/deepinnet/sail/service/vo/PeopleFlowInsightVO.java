package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人流洞察VO
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Data
@ApiModel(value = "人流洞察VO", description = "人流洞察数据信息")
public class PeopleFlowInsightVO {

    @ApiModelProperty(value = "值")
    private String val;

    @ApiModelProperty(value = "数量")
    private Long count;

    @ApiModelProperty(value = "描述")
    private String description;
}