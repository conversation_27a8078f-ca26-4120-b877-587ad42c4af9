package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;

import java.util.List;

/**
 * 线路服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface SailRouteService {

    /**
     * 获取线路分类列表（包含每个分类下的线路列表）
     *
     * @param queryDTO 查询参数（当前为空，预留扩展）
     * @return 线路分类列表
     */
    List<SailRouteCategoryVO> getRouteCategoryList(SailRouteCategoryQueryDTO queryDTO);

    /**
     * 根据条件查询线路列表
     *
     * @param queryDTO 查询条件
     * @return 线路列表
     */
    List<SailRouteListVO> getRouteList(SailRouteListQueryDTO queryDTO);

    /**
     * 根据ID获取线路详情
     *
     * @param queryDTO 查询条件
     * @return 线路详情
     */
    SailRouteDetailVO getRouteDetail(SailRouteDetailQueryDTO queryDTO);

    /**
     * 新增线路
     *
     * @param createDTO 创建参数
     * @return 线路ID
     */
    Long createRoute(SailRouteCreateDTO createDTO);

    /**
     * 编辑线路
     *
     * @param updateDTO 更新参数
     * @return 是否成功
     */
    Boolean updateRoute(SailRouteUpdateDTO updateDTO);

    /**
     * 删除线路
     *
     * @param id 线路ID
     * @return 是否成功
     */
    Boolean deleteRoute(Long id);
}
