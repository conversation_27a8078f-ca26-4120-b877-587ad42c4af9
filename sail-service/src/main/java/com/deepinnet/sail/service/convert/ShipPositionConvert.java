package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.ShipPositionDO;
import com.deepinnet.sail.service.dto.ShipPositionDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 船舶位置转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-26
 */
@Mapper(componentModel = "spring")
public interface ShipPositionConvert {

    ShipPositionDTO toDTO(ShipPositionDO shipPositionDO);

    List<ShipPositionDTO> toDTOList(List<ShipPositionDO> shipPositionDOList);
}