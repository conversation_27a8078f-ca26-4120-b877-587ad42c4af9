package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.PeopleFlowVitalityIndexDO;
import com.deepinnet.sail.dal.mapper.PeopleFlowVitalityIndexMapper;
import com.deepinnet.sail.service.repository.PeopleFlowVitalityIndexRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人流活力指数Repository实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Repository
public class PeopleFlowVitalityIndexRepositoryImpl extends ServiceImpl<PeopleFlowVitalityIndexMapper, PeopleFlowVitalityIndexDO>
        implements PeopleFlowVitalityIndexRepository {

    @Override
    public List<PeopleFlowVitalityIndexDO> findLatestByAreaCode(String areaCode) {
        // 先查询该区域的最大批次号
        LambdaQueryWrapper<PeopleFlowVitalityIndexDO> maxBatchWrapper = Wrappers.lambdaQuery(PeopleFlowVitalityIndexDO.class)
                .eq(PeopleFlowVitalityIndexDO::getAreaCode, areaCode)
                .orderByDesc(PeopleFlowVitalityIndexDO::getBatchId)
                .last("LIMIT 1");

        PeopleFlowVitalityIndexDO latestBatch = super.getOne(maxBatchWrapper);
        
        if (latestBatch == null) {
            return List.of();
        }

        // 查询该批次的所有数据
        LambdaQueryWrapper<PeopleFlowVitalityIndexDO> wrapper = Wrappers.lambdaQuery(PeopleFlowVitalityIndexDO.class)
                .eq(PeopleFlowVitalityIndexDO::getAreaCode, areaCode)
                .eq(PeopleFlowVitalityIndexDO::getBatchId, latestBatch.getBatchId());

        return super.list(wrapper);
    }
}