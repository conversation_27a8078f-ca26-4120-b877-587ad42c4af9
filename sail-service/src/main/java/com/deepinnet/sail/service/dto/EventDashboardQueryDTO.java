package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 大屏事件数据请求DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "大屏事件数据请求DTO", description = "获取大屏事件完整数据的请求参数")
public class EventDashboardQueryDTO {

    @ApiModelProperty(value = "事件来源", example = "tourist_rescue", notes = "根据事件类型查询，可选值：tourist_rescue-游客求救，video_event-视频事件，traffic_event-交通事件，active_discovery-主动发现")
    private String eventSource;

    @ApiModelProperty(value = "开始时间", example = "2025-08-01 00:00:00", notes = "事件发生时间范围查询的开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-08-31 23:59:59", notes = "事件发生时间范围查询的结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "页码", example = "1", notes = "从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10", notes = "默认10条")
    private Integer pageSize = 10;
}