package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 点位详情VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "点位详情VO", description = "点位详细信息")
public class SailPointDetailVO {

    @ApiModelProperty(value = "点位ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "点位代码", example = "SP001")
    private String code;

    @ApiModelProperty(value = "点位名称", example = "深圳港1号码头")
    private String name;

    @ApiModelProperty(value = "点位类型", example = "port_terminal")
    private String type;

    @ApiModelProperty(value = "点位类型名称", example = "港口码头")
    private String typeName;

    @ApiModelProperty(value = "点位地址", example = "深圳市南山区蛇口港")
    private String address;

    @ApiModelProperty(value = "点位坐标", example = "POINT(114.057868 22.543099)")
    private String wkt;

    @ApiModelProperty(value = "点位来源", example = "userDefined", 
            notes = "可能值：userDefined(自建点位), gaoDe(高德同步的点位)")
    private String source;

    @ApiModelProperty(value = "创建时间", example = "2025-01-15T10:30:00")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间", example = "2025-01-15T10:30:00")
    private LocalDateTime gmtModified;
}

