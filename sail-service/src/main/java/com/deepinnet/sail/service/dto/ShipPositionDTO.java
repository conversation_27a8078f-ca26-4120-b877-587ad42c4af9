package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 船舶位置信息DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-26
 */
@Data
public class ShipPositionDTO {

    @ApiModelProperty(value = "船舶编号", example = "CB9837216")
    private String shipNo;

    @ApiModelProperty(value = "船舶名称", example = "南澳旅游096")
    private String shipName;

    @ApiModelProperty(value = "经度", example = "114.123456")
    private String x;

    @ApiModelProperty(value = "纬度", example = "22.654321")
    private String y;
}
