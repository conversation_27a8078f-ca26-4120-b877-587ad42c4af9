package com.deepinnet.sail.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 事件服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
public interface SailEventService {

    /**
     * 分页查询事件列表
     *
     * @param queryDTO 查询条件
     * @return 事件分页数据
     */
    CommonPage<EventVO> pageEvents(EventQueryDTO queryDTO);

    /**
     * 根据事件编号查询事件详情
     *
     * @param eventNo 事件编号
     * @return 事件详情
     */
    EventVO getEventDetail(String eventNo);

    /**
     * 上报事件
     *
     * @param reportDTO 上报数据
     * @return 事件ID
     */
    String reportEvent(EventReportDTO reportDTO);

    /**
     * 处理事件
     *
     * @param processDTO 处理数据
     * @return 处理记录ID
     */
    Long processEvent(EventProcessDTO processDTO);

    /**
     * 删除事件
     *
     * @param eventNo 事件编号
     * @return 是否成功
     */
    Boolean deleteEvent(String eventNo);

    /**
     * 导出事件列表
     *
     * @param queryDTO 查询条件
     */
    void exportEvents(EventQueryDTO queryDTO, HttpServletResponse response);

    /**
     * 获取大屏事件数据（包含统计和列表）
     *
     * @param requestDTO 请求参数
     * @return 大屏事件数据
     */
    EventDashboardVO pageQueryDashboardEvents(EventDashboardQueryDTO requestDTO);
}
