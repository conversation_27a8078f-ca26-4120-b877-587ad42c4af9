package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.ShipDetailVO;

import java.util.List;

/**
 * 船舶服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
public interface ShipService {

    /**
     * 查询所有船舶详细信息（含位置坐标）
     *
     * @param queryDTO 查询条件
     * @return 船舶详细信息列表
     */
    List<ShipDetailVO> getAllShipDetail(ShipDetailQueryDTO queryDTO);

    /**
     * 查询所有船舶最新位置
     *
     * @param queryDTO
     * @return
     */
    List<ShipPositionDTO> getAllShipLatestPosition(ShipPositionQueryDTO queryDTO);
}
