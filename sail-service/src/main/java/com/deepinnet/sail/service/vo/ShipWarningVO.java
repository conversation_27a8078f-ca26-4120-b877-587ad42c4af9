package com.deepinnet.sail.service.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 船舶预警记录VO
 *
 * <AUTHOR> wong
 * @since 2025-08-22
 */
@Data
@ApiModel(value = "船舶预警记录VO", description = "船舶预警记录信息")
public class ShipWarningVO {

    @ApiModelProperty(value = "预警ID")
    private Long id;

    @ApiModelProperty(value = "船舶编号")
    private String shipNo;

    @ApiModelProperty(value = "船舶名称")
    private String shipName;

    @ApiModelProperty(value = "所属片区")
    private String portCode;

    @ApiModelProperty(value = "所属片区名称")
    private String portName;

    @ApiModelProperty(value = "船舶所有人")
    private String owner;

    @ApiModelProperty(value = "船主联系电话")
    private String ownerPhone;

    @ApiModelProperty(value = "预警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime triggerTime;

    @ApiModelProperty(value = "预警描述")
    private String description;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "经度")
    private String x;

    @ApiModelProperty(value = "纬度")
    private String y;

    @ApiModelProperty(value = "出海记录ID")
    private Long sailingRecordId;

    @ApiModelProperty(value = "纳管公司")
    private String managementCompany;

    @ApiModelProperty(value = "包干责任人")
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人联系电话")
    private String responsiblePersonPhone;

    @ApiModelProperty(value = "所属办事处")
    private String office;

    @ApiModelProperty(value = "通知状态")
    private String noticeStatus;
}
