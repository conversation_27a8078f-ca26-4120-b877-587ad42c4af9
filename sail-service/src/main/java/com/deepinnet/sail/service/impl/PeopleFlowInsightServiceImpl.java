package com.deepinnet.sail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO;
import com.deepinnet.sail.service.PeopleFlowInsightService;
import com.deepinnet.sail.service.convert.PeopleFlowInsightConvert;
import com.deepinnet.sail.service.dto.PeopleFlowInsightQueryDTO;
import com.deepinnet.sail.service.repository.SailPeopleFlowInsightRepository;
import com.deepinnet.sail.service.vo.PeopleFlowInsightVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 人流洞察服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Service
@RequiredArgsConstructor
public class PeopleFlowInsightServiceImpl implements PeopleFlowInsightService {

    private final SailPeopleFlowInsightRepository peopleFlowInsightRepository;

    private final PeopleFlowInsightConvert peopleFlowInsightConvert;

    @Override
    public List<PeopleFlowInsightVO> queryLatestInsightData(PeopleFlowInsightQueryDTO queryDTO) {
        // 1. 先获取最新的 requestTime
        LambdaQueryWrapper<SailPeopleFlowInsightDO> maxTimeWrapper = Wrappers.lambdaQuery(SailPeopleFlowInsightDO.class)
                .select(SailPeopleFlowInsightDO::getRequestTime)
                .eq(SailPeopleFlowInsightDO::getAreaCode, queryDTO.getAreaCode())
                .eq(SailPeopleFlowInsightDO::getProfile, queryDTO.getProfile())
                .orderByDesc(SailPeopleFlowInsightDO::getRequestTime)
                .last("LIMIT 1");

        SailPeopleFlowInsightDO latestRecord = peopleFlowInsightRepository.getOne(maxTimeWrapper);
        if (latestRecord == null || latestRecord.getRequestTime() == null) {
            LogUtil.info("未找到人流洞察数据，profile: {}", queryDTO.getProfile());
            return Collections.emptyList();
        }

        Long latestRequestTime = latestRecord.getRequestTime();
        LogUtil.info("查询最新人流洞察数据，latestRequestTime: {}, profile: {}", latestRequestTime, queryDTO.getProfile());

        // 2. 根据最新的 requestTime 查询所有数据
        LambdaQueryWrapper<SailPeopleFlowInsightDO> dataWrapper = Wrappers.lambdaQuery(SailPeopleFlowInsightDO.class)
                .eq(SailPeopleFlowInsightDO::getAreaCode, queryDTO.getAreaCode())
                .eq(SailPeopleFlowInsightDO::getRequestTime, latestRequestTime)
                .eq(SailPeopleFlowInsightDO::getProfile, queryDTO.getProfile())
                .orderByAsc(SailPeopleFlowInsightDO::getVal);

        List<SailPeopleFlowInsightDO> dataList = peopleFlowInsightRepository.list(dataWrapper);
        if (CollectionUtils.isEmpty(dataList)) {
            LogUtil.info("未找到对应的人流洞察数据，requestTime: {}, profile: {}", latestRequestTime, queryDTO.getProfile());
            return Collections.emptyList();
        }

        // 3. 转换为 VO 并返回
        List<PeopleFlowInsightVO> result = peopleFlowInsightConvert.toVOList(dataList);
        LogUtil.info("查询人流洞察数据成功，返回{}条记录", result.size());
        return result;
    }
}