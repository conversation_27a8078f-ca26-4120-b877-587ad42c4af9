package com.deepinnet.sail.service.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.condition.ShipWarningQueryCondition;
import com.deepinnet.sail.dal.dataobject.ShipWarningDO;
import com.deepinnet.sail.dal.dto.ShipWarningDTO;
import com.deepinnet.sail.dal.dto.WarningTypeStatisticsDTO;
import com.deepinnet.sail.dal.mapper.ShipWarningMapper;
import com.deepinnet.sail.service.repository.ShipWarningRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 船舶预警表 Repository 实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
@Service
public class ShipWarningRepositoryImpl extends ServiceImpl<ShipWarningMapper, ShipWarningDO>
        implements ShipWarningRepository {

    @Override
    public ShipWarningDO findByRecordIdAndType(Long sailingRecordId, String warningType) {
        if (sailingRecordId == null || StrUtil.isBlank(warningType)) {
            return null;
        }

        LambdaQueryWrapper<ShipWarningDO> wrapper = Wrappers.lambdaQuery(ShipWarningDO.class)
                .eq(ShipWarningDO::getSailingRecordId, sailingRecordId)
                .eq(ShipWarningDO::getWarningType, warningType)
                .last("LIMIT 1");

        return super.getOne(wrapper);
    }

    @Override
    public List<ShipWarningDTO> selectShipWarningsWithShipInfo(ShipWarningQueryCondition queryCondition) {
        return baseMapper.selectShipWarningsWithShipInfo(queryCondition);
    }

    @Override
    public List<WarningTypeStatisticsDTO> getWarningTypeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getWarningTypeStatistics(startTime, endTime);
    }
}
