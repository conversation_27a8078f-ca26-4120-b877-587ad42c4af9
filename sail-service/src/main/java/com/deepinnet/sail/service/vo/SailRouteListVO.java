package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线路列表VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "线路列表VO", description = "线路列表信息")
public class SailRouteListVO {

    @ApiModelProperty(value = "线路ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "线路代码", example = "SR001")
    private String code;

    @ApiModelProperty(value = "线路名称", example = "深圳港主航道1")
    private String name;

    @ApiModelProperty(value = "线路类型", example = "main_channel")
    private String type;

    @ApiModelProperty(value = "线路类型名称", example = "主航道")
    private String typeName;

    @ApiModelProperty(value = "线路长度", example = "1500.5")
    private String length;

    @ApiModelProperty(value = "线路来源", example = "userDefined", 
            notes = "可能值：userDefined(自建线路), gaoDe(高德同步的线路)")
    private String source;

    @ApiModelProperty(value = "创建时间", example = "2025-01-15T10:30:00")
    private LocalDateTime gmtCreated;
}
