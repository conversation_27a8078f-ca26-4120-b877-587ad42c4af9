package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 线路详情查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "线路详情查询DTO", description = "查询线路详情的请求参数")
public class SailRouteDetailQueryDTO {

    @ApiModelProperty(value = "线路ID", example = "1", required = true)
    @NotNull(message = "线路ID不能为空")
    private Long id;
}
