package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件来源枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum EventSourceEnum {

    /**
     * 游客求救
     */
    TOURIST_RESCUE("tourist_rescue", "游客求救"),

    /**
     * 视频事件
     */
    VIDEO_EVENT("video_event", "视频事件"),

    /**
     * 交通事件
     */
    TRAFFIC_EVENT("traffic_event", "交通事件"),

    /**
     * 主动发现
     */
    ACTIVE_DISCOVERY("active_discovery", "主动发现");

    /**
     * 来源代码
     */
    private final String code;

    /**
     * 来源描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static EventSourceEnum getByCode(String code) {
        for (EventSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }
}
