package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.ShipDO;
import com.deepinnet.sail.dal.mapper.ShipMapper;
import com.deepinnet.sail.service.repository.ShipRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 船舶信息表 服务实现类
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Service
public class ShipRepositoryImpl extends ServiceImpl<ShipMapper, ShipDO> implements ShipRepository {

}
