package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询所有船舶DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@ApiModel(value = "查询所有船舶DTO", description = "查询所有船舶详细信息的请求参数")
@Data
public class ShipDetailQueryDTO {

    @ApiModelProperty(value = "港口code")
    @NotBlank(message = "港口code不能为空")
    private String portCode;
}