package com.deepinnet.sail.service.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 出海记录VO
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Data
@ApiModel(value = "出海记录VO")
public class SailingRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "出海记录ID", example = "1001", notes = "唯一标识")
    private Long id;

    @ApiModelProperty(value = "船舶编号", example = "SHIP001", notes = "船舶唯一编号")
    private String shipNo;

    @ApiModelProperty(value = "船舶名称", example = "渔船001", notes = "船舶名称")
    private String shipName;

    @ApiModelProperty(value = "港口编号", example = "PORT001", notes = "船舶所属港口编号")
    private String portCode;

    @ApiModelProperty(value = "港口名称", example = "PORT001", notes = "船舶所属港口编号")
    private String portName;

    @ApiModelProperty(value = "出港时间", example = "2025/07/01 10:30", notes = "船舶离港时间，格式：yyyy/MM/dd HH:mm")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    private LocalDateTime departureTime;

    @ApiModelProperty(value = "归港时间", example = "2025/07/01 16:30", notes = "船舶归港时间，未归港时为null，格式：yyyy/MM/dd HH:mm")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    private LocalDateTime returnTime;

    @ApiModelProperty(value = "归港状态", example = "SAILING", notes = "SAILING-未归，RETURNED-已归")
    private String status;

    @ApiModelProperty(value = "预警信息列表", notes = "该出海记录关联的所有预警信息")
    private List<ShipWarningInfoVO> warnings;
}
