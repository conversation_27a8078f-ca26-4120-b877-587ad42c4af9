package com.deepinnet.sail.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.service.enums.ProfileEnum;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.spatiotemporalplatform.client.sail.SailPeopleFlowInsightClient;
import com.deepinnet.spatiotemporalplatform.model.sail.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 人流洞察数据定时任务
 * 负责定时获取和保存人流洞察数据
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Component
public class SailPeopleFlowInsightTask {

    @Resource
    private SailPeopleFlowInsightRepository peopleFlowInsightRepository;

    @Resource
    private SailPeopleFlowInsightClient sailPeopleFlowInsightClient;

    @Resource
    private SeaAreaRepository seaAreaRepository;

    /**
     * 定时获取人流洞察数据
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    // @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    public void fetchAndSavePeopleFlowInsight() {
        long requestTime = System.currentTimeMillis();

        try {
            List<SeaAreaDO> seaAreaList = seaAreaRepository.list(Wrappers.lambdaQuery(SeaAreaDO.class));
            if (CollUtil.isEmpty(seaAreaList)) {
                LogUtil.info("系统区域表为空，无法获取人流洞察数据");
                return;
            }

            for (SeaAreaDO seaAreaDO : seaAreaList) {
                // 获取各个维度的人流洞察数据
                List<SailPeopleFlowInsightDO> allDataList = new ArrayList<>();

                // 性别
                invokeGaoDeAndGenerateAssembleInsightDO(seaAreaDO.getCode(), ProfileEnum.U_SEX.getCode(), requestTime, seaAreaDO.getAdCode(), allDataList);

                // 职业
                invokeGaoDeAndGenerateAssembleInsightDO(seaAreaDO.getCode(), ProfileEnum.U_OCCUPATION.getCode(), requestTime, seaAreaDO.getAdCode(), allDataList);

                // 年龄
                invokeGaoDeAndGenerateAssembleInsightDO(seaAreaDO.getCode(), ProfileEnum.U_AGE.getCode(), requestTime, seaAreaDO.getAdCode(), allDataList);

                // 常驻区域
                invokeGaoDeAndGenerateAssembleInsightDO(seaAreaDO.getCode(), ProfileEnum.U_PERMANENT_ADCODE.getCode(), requestTime, seaAreaDO.getAdCode(), allDataList);

                //  资产分布
                invokeGaoDeAndGenerateAssembleInsightDO(seaAreaDO.getCode(), ProfileEnum.PROPERTY_LEVEL.getCode(), requestTime, seaAreaDO.getAdCode(), allDataList);

                if (CollUtil.isNotEmpty(allDataList)) {
                    // 批量保存数据
                    boolean success = peopleFlowInsightRepository.saveBatch(allDataList);
                    if (success) {
                        LogUtil.info("人流洞察数据定时任务执行成功，共保存{}条记录", allDataList.size());
                    } else {
                        LogUtil.error("人流洞察数据保存失败");
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error("人流洞察数据定时任务执行失败", e);
            throw e;
        }
    }


    private void invokeGaoDeAndGenerateAssembleInsightDO(String areaCode, String profile, Long timestamp, String adCode, List<SailPeopleFlowInsightDO> allDataList) {
        SailPeopleFlowInsightUrlParams urlParams = new SailPeopleFlowInsightUrlParams();
        urlParams.setCustomAreaId(areaCode);
        urlParams.setProfile(profile);
        urlParams.setTimestamp(timestamp);
        urlParams.setAdcode(adCode);
        urlParams.setIsPersistence(false);

        Result<List<SailPeopleFlowInsightDTO>> result = sailPeopleFlowInsightClient.getRealTimePeopleFlowInsight(urlParams);
        if (!result.isSuccess()) {
            LogUtil.error("调用高德人流洞察接口失败，入参为：areaCode={},profile={},timestamp={},adCode={}，异常code:{},desc={}", areaCode, profile, timestamp, adCode, result.getErrorCode(), result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        if (result.getData() == null) {
            LogUtil.info("调用高德人流洞察接口，查询到的数据为空，入参为：areaCode={},profile={},timestamp={},adCode={}，响应为：{}", areaCode, profile, timestamp, adCode, JSONUtil.toJsonStr(result));
            return;
        }

        List<SailPeopleFlowInsightDTO> dataList = result.getData();

        convertAndAssembleInsightDO(areaCode, timestamp, profile, dataList, allDataList);
    }

    private void convertAndAssembleInsightDO(String areaCode, Long requestTime, String profile, List<SailPeopleFlowInsightDTO> dataList, List<SailPeopleFlowInsightDO> allDataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }

        dataList.forEach(d -> {
            SailPeopleFlowInsightDO insightDO = new SailPeopleFlowInsightDO();
            insightDO.setAreaCode(areaCode);
            insightDO.setVal(d.getVal());
            insightDO.setCount(d.getIndex());
            insightDO.setDescription(getDescByVal(d.getVal(), profile));
            insightDO.setProfile(profile);
            insightDO.setRequestTime(requestTime);
            insightDO.setGmtCreated(LocalDateTime.now());
            insightDO.setGmtModified(LocalDateTime.now());
            allDataList.add(insightDO);
        });
    }

    private String getDescByVal(String val, String profile) {
        if (StrUtil.equals(profile, ProfileEnum.U_SEX.getCode())) {
            if (StrUtil.equals(val, "1")) {
                return "男";
            }
            if (StrUtil.equals(val, "2")) {
                return "女";
            }

            return "未知";
        } else if (StrUtil.equals(profile, ProfileEnum.PROPERTY_LEVEL.getCode())) {
            if (StrUtil.equals(val, "1")) {
                return "超级富豪";
            }
            if (StrUtil.equals(val, "2")) {
                return "富豪";
            }

            if (StrUtil.equals(val, "3")) {
                return "中产";
            }

            if (StrUtil.equals(val, "4")) {
                return "工薪一族";
            }

            return "未知";
        } else {
            return val;
        }
    }
}