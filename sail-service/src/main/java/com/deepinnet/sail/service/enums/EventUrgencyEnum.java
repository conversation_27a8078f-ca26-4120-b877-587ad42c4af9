package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件紧急程度枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum EventUrgencyEnum {

    /**
     * 一般
     */
    NORMAL("normal", "一般"),

    /**
     * 紧急
     */
    URGENT("urgent", "紧急"),

    /**
     * 非常紧急
     */
    VERY_URGENT("very_urgent", "非常紧急");

    /**
     * 紧急程度代码
     */
    private final String code;

    /**
     * 紧急程度描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static EventUrgencyEnum getByCode(String code) {
        for (EventUrgencyEnum urgency : values()) {
            if (urgency.getCode().equals(code)) {
                return urgency;
            }
        }
        return null;
    }
}
