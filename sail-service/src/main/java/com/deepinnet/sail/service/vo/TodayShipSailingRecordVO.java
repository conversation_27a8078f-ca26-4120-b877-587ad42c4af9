package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 今日船舶出海记录VO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel("今日船舶出海记录VO")
public class TodayShipSailingRecordVO {

    @ApiModelProperty("船舶名称")
    private String shipName;

    @ApiModelProperty("出港时间")
    private LocalDateTime departureTime;

    @ApiModelProperty("归港状态")
    private String returnStatus;

    @ApiModelProperty("预警信息")
    private List<String> shipWarningList;

    @ApiModelProperty("船舶编号")
    private String shipNo;
}
