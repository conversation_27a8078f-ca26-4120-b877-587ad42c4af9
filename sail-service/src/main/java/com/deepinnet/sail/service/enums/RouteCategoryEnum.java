package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线路分类枚举
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@AllArgsConstructor
public enum RouteCategoryEnum {

    /**
     * 主航道
     */
    MAIN_CHANNEL("main_channel", "主航道"),

    /**
     * 支航道
     */
    BRANCH_CHANNEL("branch_channel", "支航道"),

    /**
     * 进港航道
     */
    APPROACH_CHANNEL("approach_channel", "进港航道"),

    /**
     * 出港航道
     */
    DEPARTURE_CHANNEL("departure_channel", "出港航道"),

    /**
     * 渔业航道
     */
    FISHING_ROUTE("fishing_route", "渔业航道"),

    /**
     * 旅游航线
     */
    TOURISM_ROUTE("tourism_route", "旅游航线"),

    /**
     * 货运航线
     */
    CARGO_ROUTE("cargo_route", "货运航线"),

    /**
     * 应急航线
     */
    EMERGENCY_ROUTE("emergency_route", "应急航线"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static RouteCategoryEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (RouteCategoryEnum categoryEnum : RouteCategoryEnum.values()) {
            if (categoryEnum.getCode().equals(code)) {
                return categoryEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述，如果不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        RouteCategoryEnum categoryEnum = getByCode(code);
        return categoryEnum != null ? categoryEnum.getDesc() : "";
    }

    /**
     * 验证分类编码是否有效
     *
     * @param code 编码
     * @return true如果有效，false如果无效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
