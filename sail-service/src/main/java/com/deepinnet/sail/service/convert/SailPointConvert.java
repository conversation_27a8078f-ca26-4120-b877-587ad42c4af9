package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.SailPointDO;
import com.deepinnet.sail.service.dto.SailPointCreateDTO;
import com.deepinnet.sail.service.dto.SailPointUpdateDTO;
import com.deepinnet.sail.service.enums.PointCategoryEnum;
import com.deepinnet.sail.service.vo.SailPointDetailVO;
import com.deepinnet.sail.service.vo.SailPointListVO;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;

import java.util.List;

/**
 * 点位转换器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = "spring")
public interface SailPointConvert {

    /**
     * DO转换为列表VO
     *
     * @param sailPointDO DO对象
     * @return 列表VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    SailPointListVO toListVO(SailPointDO sailPointDO);

    /**
     * DO列表转换为列表VO列表
     *
     * @param sailPointDOList DO列表
     * @return 列表VO列表
     */
    List<SailPointListVO> toListVOList(List<SailPointDO> sailPointDOList);

    /**
     * DO转换为详情VO
     *
     * @param sailPointDO DO对象
     * @return 详情VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "pointToString")
    SailPointDetailVO toDetailVO(SailPointDO sailPointDO);

    /**
     * 创建DTO转换为DO
     *
     * @param createDTO 创建DTO
     * @return DO对象
     */
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "stringToPoint")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "source", constant = "userDefined")
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SailPointDO fromCreateDTO(SailPointCreateDTO createDTO);

    /**
     * 更新DTO转换为DO
     *
     * @param updateDTO 更新DTO
     * @return DO对象
     */
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "stringToPoint")
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SailPointDO fromUpdateDTO(SailPointUpdateDTO updateDTO);

    /**
     * 根据类型编码获取类型名称
     *
     * @param typeCode 类型编码
     * @return 类型名称
     */
    @Named("getTypeName")
    default String getTypeName(String typeCode) {
        return PointCategoryEnum.getDescByCode(typeCode);
    }

    /**
     * Point转换为WKT字符串
     * 使用WktUtil进行转换
     *
     * @param point Point对象
     * @return WKT字符串
     */
    @Named("pointToString")
    default String pointToString(Point point) {
        return WktUtil.toWkt(point);
    }

    /**
     * WKT字符串转换为Point
     * 使用WktUtil进行转换
     *
     * @param wktString WKT字符串
     * @return Point对象
     */
    @Named("stringToPoint")
    default Point stringToPoint(String wktString) {
        Geometry geometry = WktUtil.toGeometry(wktString);
        if (geometry instanceof Point) {
            return (Point) geometry;
        } else {
            throw new RuntimeException("WKT字符串不是Point类型: " + wktString);
        }
    }
}

