package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.PeopleFlowInsightQueryDTO;
import com.deepinnet.sail.service.vo.PeopleFlowInsightVO;

import java.util.List;

/**
 * 人流洞察服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
public interface PeopleFlowInsightService {

    /**
     * 查询最新的人流洞察数据
     *
     * @param queryDTO 查询参数
     * @return 人流洞察数据列表
     */
    List<PeopleFlowInsightVO> queryLatestInsightData(PeopleFlowInsightQueryDTO queryDTO);
}