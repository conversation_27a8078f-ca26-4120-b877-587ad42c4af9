package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.SailRouteDO;

import java.util.List;

/**
 * 线路表 Repository 接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface SailRouteRepository extends IService<SailRouteDO> {

    /**
     * 查询所有用户自定义线路列表，按创建时间倒序排序
     *
     * @return 线路列表
     */
    List<SailRouteDO> findUserDefinedRoutesOrderByCreateTimeDesc();

    /**
     * 根据分类查询用户自定义线路列表，按创建时间倒序排序
     *
     * @param category 线路分类
     * @return 线路列表
     */
    List<SailRouteDO> findUserDefinedRoutesByCategoryOrderByCreateTimeDesc(String category);

    /**
     * 根据名称模糊查询用户自定义线路列表，按创建时间倒序排序
     *
     * @param name 线路名称
     * @return 线路列表
     */
    List<SailRouteDO> findUserDefinedRoutesByNameLikeOrderByCreateTimeDesc(String name);

    /**
     * 根据分类和名称查询用户自定义线路列表，按创建时间倒序排序
     *
     * @param category 线路分类
     * @param name 线路名称
     * @return 线路列表
     */
    List<SailRouteDO> findUserDefinedRoutesByCategoryAndNameLikeOrderByCreateTimeDesc(String category, String name);

    /**
     * 检查线路名称是否已存在（排除指定ID）
     *
     * @param name 线路名称
     * @param excludeId 排除的线路ID，可为null
     * @return true如果存在，false如果不存在
     */
    boolean existsByNameAndIdNot(String name, Long excludeId);

    /**
     * 根据ID查询用户自定义线路
     *
     * @param id 线路ID
     * @return 线路信息
     */
    SailRouteDO findUserDefinedRouteById(Long id);
}
