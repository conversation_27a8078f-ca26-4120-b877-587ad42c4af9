package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.SeaAreaDO;

import java.util.List;

/**
 * 海域表 Repository 接口
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
public interface SeaAreaRepository extends IService<SeaAreaDO> {

    /**
     * 根据类型查询海域/港口列表
     *
     * @param type 海域类型
     * @return 海域列表
     */
    List<SeaAreaDO> findByType(String type);

    /**
     * 查询所有用户自定义区域列表，按创建时间倒序排序
     *
     * @return 区域列表
     */
    List<SeaAreaDO> findUserDefinedAreasOrderByCreateTimeDesc();

    /**
     * 根据分类查询用户自定义区域列表，按创建时间倒序排序
     *
     * @param category 区域分类
     * @return 区域列表
     */
    List<SeaAreaDO> findUserDefinedAreasByCategoryOrderByCreateTimeDesc(String category);

    /**
     * 根据名称模糊查询用户自定义区域列表，按创建时间倒序排序
     *
     * @param name 区域名称
     * @return 区域列表
     */
    List<SeaAreaDO> findUserDefinedAreasByNameLikeOrderByCreateTimeDesc(String name);

    /**
     * 根据分类和名称查询用户自定义区域列表，按创建时间倒序排序
     *
     * @param category 区域分类
     * @param name 区域名称
     * @return 区域列表
     */
    List<SeaAreaDO> findUserDefinedAreasByCategoryAndNameLikeOrderByCreateTimeDesc(String category, String name);

    /**
     * 检查区域名称是否已存在（排除指定ID）
     *
     * @param name 区域名称
     * @param excludeId 排除的区域ID，可为null
     * @return true如果存在，false如果不存在
     */
    boolean existsByNameAndIdNot(String name, Long excludeId);

    /**
     * 根据ID查询用户自定义区域
     *
     * @param id 区域ID
     * @return 区域信息
     */
    SeaAreaDO findUserDefinedAreaById(Long id);

    /**
     * 根据区域编码查询用户自定义区域列表，按创建时间倒序排序
     *
     * @param areaCode 区域编码
     * @return 区域列表
     */
    List<SeaAreaDO> findUserDefinedAreasByAreaCodeOrderByCreateTimeDesc(String areaCode);

    /**
     * 检查区域编码是否已存在（排除指定ID）
     *
     * @param areaCode 区域编码
     * @param excludeId 排除的区域ID，可为null
     * @return true如果存在，false如果不存在
     */
    boolean existsByAreaCodeAndIdNot(String areaCode, Long excludeId);
}
