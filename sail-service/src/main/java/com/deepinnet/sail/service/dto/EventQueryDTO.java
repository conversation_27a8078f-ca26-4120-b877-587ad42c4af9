package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事件查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件查询DTO", description = "事件列表查询参数")
public class EventQueryDTO {

    @ApiModelProperty(value = "事件描述", example = "游客遇险", notes = "模糊匹配查询")
    private String eventDescription;

    @ApiModelProperty(value = "事件来源", example = "tourist_rescue", notes = "精准查询，可选值：tourist_rescue-游客求救，video_event-视频事件，traffic_event-交通事件，active_discovery-主动发现")
    private String eventSource;

    @ApiModelProperty(value = "紧急程度", example = "urgent", notes = "精准查询，可选值：normal-一般，urgent-紧急，very_urgent-非常紧急")
    private String urgencyLevel;

    @ApiModelProperty(value = "事件状态", example = "pending", notes = "精准查询，可选值：pending-待处理，processing-处理中，completed-已办结，closed-已关闭")
    private String status;

    @ApiModelProperty(value = "页码", example = "1", notes = "从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10", notes = "默认10条")
    private Integer pageSize = 10;
}
