package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.ShipPositionDO;
import com.deepinnet.sail.dal.mapper.ShipPositionMapper;
import com.deepinnet.sail.service.repository.ShipPositionRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 船舶位置信息表 服务实现类
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Service
public class ShipPositionRepositoryImpl extends ServiceImpl<ShipPositionMapper, ShipPositionDO> implements ShipPositionRepository {

    @Override
    public List<ShipPositionDO> getLatestPositionsByTimeRange(Long startTime) {
        return baseMapper.getLatestPositionsByTimeRange(startTime);
    }

    @Override
    public List<ShipPositionDO> getLatestPositionsByShipNosWithTimeLimit(List<String> shipNos, Long oneHourAgo) {
        return baseMapper.getLatestPositionsByShipNos(shipNos, oneHourAgo);
    }
}
