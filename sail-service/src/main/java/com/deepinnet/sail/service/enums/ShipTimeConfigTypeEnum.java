package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 船舶时间配置类型枚举
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum ShipTimeConfigTypeEnum {

    /**
     * 最迟归港时间
     */
    DAILY_RETURN_TIME("daily_return_time", "最迟归港时间"),

    /**
     * 常规风险时段
     */
    REGULAR_RISK_TIME("regular_risk_time", "常规风险时段"),

    /**
     * 自定义风险时段
     */
    CUSTOM_RISK_TIME("custom_risk_time", "自定义风险时段"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static ShipTimeConfigTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ShipTimeConfigTypeEnum typeEnum : ShipTimeConfigTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述，如果不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        ShipTimeConfigTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getDesc() : "";
    }
}
