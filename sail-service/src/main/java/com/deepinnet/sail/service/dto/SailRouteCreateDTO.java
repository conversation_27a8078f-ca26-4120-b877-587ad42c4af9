package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 线路新增DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "线路新增DTO", description = "新增线路的请求参数")
public class SailRouteCreateDTO {

    @ApiModelProperty(value = "线路类型", example = "main_channel", 
            notes = "可能值：main_channel(主航道), branch_channel(支航道), approach_channel(进港航道), departure_channel(出港航道), fishing_route(渔业航道), tourism_route(旅游航线), cargo_route(货运航线), emergency_route(应急航线)", required = true)
    @NotBlank(message = "线路类型不能为空")
    private String type;

    @ApiModelProperty(value = "线路名称", example = "深圳港主航道1", notes = "手动输入，最大长度：20", required = true)
    @NotBlank(message = "线路名称不能为空")
    @Size(max = 20, message = "线路名称长度不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "线路长度", example = "1500.5", notes = "根据右侧所绘线路范围自动计算线路长度")
    private String length;

    @ApiModelProperty(value = "线路坐标", example = "LINESTRING(114.057868 22.543099, 114.064407 22.543099, 114.064407 22.547056)", 
            notes = "WKT格式的线路坐标信息", required = true)
    @NotBlank(message = "线路坐标不能为空")
    private String wkt;
}
