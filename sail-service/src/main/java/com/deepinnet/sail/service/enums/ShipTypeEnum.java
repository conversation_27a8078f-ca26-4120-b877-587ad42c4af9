package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 船舶类型枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum ShipTypeEnum {

    /**
     * 运营船舶
     */
    OPERATIONAL("operation_management", "运营船舶"),

    /**
     * 自用船舶
     */
    PRIVATE("self_use", "自用船舶");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static ShipTypeEnum getByCode(String code) {
        for (ShipTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
