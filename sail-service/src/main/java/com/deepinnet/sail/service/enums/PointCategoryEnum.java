package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点位类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@AllArgsConstructor
public enum PointCategoryEnum {

    /**
     * 港口码头
     */
    PORT_TERMINAL("port_terminal", "港口码头"),

    /**
     * 锚泊点
     */
    ANCHORAGE_POINT("anchorage_point", "锚泊点"),

    /**
     * 灯塔
     */
    LIGHTHOUSE("lighthouse", "灯塔"),

    /**
     * 信标
     */
    BEACON("beacon", "信标"),

    /**
     * 监控点
     */
    MONITORING_POINT("monitoring_point", "监控点"),

    /**
     * 救援站
     */
    RESCUE_STATION("rescue_station", "救援站"),

    /**
     * 加油站
     */
    FUEL_STATION("fuel_station", "加油站"),

    /**
     * 检查点
     */
    CHECKPOINT("checkpoint", "检查点"),

    /**
     * 危险点
     */
    DANGER_POINT("danger_point", "危险点"),

    /**
     * 其他
     */
    OTHER("other", "其他");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static PointCategoryEnum getByCode(String code) {
        for (PointCategoryEnum categoryEnum : values()) {
            if (categoryEnum.getCode().equals(code)) {
                return categoryEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PointCategoryEnum categoryEnum = getByCode(code);
        return categoryEnum != null ? categoryEnum.getDesc() : null;
    }

    /**
     * 验证编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}

