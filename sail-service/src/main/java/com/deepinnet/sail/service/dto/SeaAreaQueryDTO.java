package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 海域类型查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "海域类型查询DTO", description = "根据类型查询海域/港口的请求参数")
public class SeaAreaQueryDTO {

    @ApiModelProperty(value = "海域类型", example = "port", 
            notes = "可能值：forbidden(禁海区), normal(普通海域), port(港口)", required = true)
    @NotBlank(message = "海域类型不能为空")
    private String type;
}