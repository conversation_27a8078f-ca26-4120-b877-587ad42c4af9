package com.deepinnet.sail.service.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件响应VO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件响应VO", description = "事件信息")
public class EventVO {
    @ApiModelProperty(value = "事件编号")
    private String eventNo;

    @ApiModelProperty(value = "事件来源代码")
    private String eventSource;

    @ApiModelProperty(value = "事件描述")
    private String eventDescription;

    @ApiModelProperty(value = "紧急程度代码")
    private String urgencyLevel;

    @ApiModelProperty(value = "事发地址")
    private String incidentAddress;

    @ApiModelProperty(value = "经度")
    private String x;

    @ApiModelProperty(value = "纬度")
    private String y;

    @ApiModelProperty(value = "事发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime incidentTime;

    @ApiModelProperty(value = "上报部门")
    private String reportDepartment;

    @ApiModelProperty(value = "上报人")
    private String reporterName;

    @ApiModelProperty(value = "上报人手机号")
    private String reporterPhone;

    @ApiModelProperty(value = "上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportTime;


    @ApiModelProperty(value = "办结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;


    @ApiModelProperty(value = "事件状态代码")
    private String status;

    @ApiModelProperty(value = "附件列表")
    private List<String> attachmentUrls;

    @ApiModelProperty(value = "处置过程")
    private List<EventProcessVO> processEventDTOList;
}
