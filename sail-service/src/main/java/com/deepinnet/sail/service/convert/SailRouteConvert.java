package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.SailRouteDO;
import com.deepinnet.sail.service.dto.SailRouteCreateDTO;
import com.deepinnet.sail.service.dto.SailRouteUpdateDTO;
import com.deepinnet.sail.service.enums.RouteCategoryEnum;
import com.deepinnet.sail.service.vo.SailRouteDetailVO;
import com.deepinnet.sail.service.vo.SailRouteListVO;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * 线路转换器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = "spring")
public interface SailRouteConvert {

    /**
     * DO转换为列表VO
     *
     * @param sailRouteDO DO对象
     * @return 列表VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    SailRouteListVO toListVO(SailRouteDO sailRouteDO);

    /**
     * DO列表转换为列表VO列表
     *
     * @param sailRouteDOList DO列表
     * @return 列表VO列表
     */
    List<SailRouteListVO> toListVOList(List<SailRouteDO> sailRouteDOList);

    /**
     * DO转换为详情VO
     *
     * @param sailRouteDO DO对象
     * @return 详情VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "lineStringToString")
    SailRouteDetailVO toDetailVO(SailRouteDO sailRouteDO);

    /**
     * 创建DTO转换为DO
     *
     * @param createDTO 创建DTO
     * @return DO对象
     */
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "stringToLineString")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "source", constant = "userDefined")
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SailRouteDO fromCreateDTO(SailRouteCreateDTO createDTO);

    /**
     * 更新DTO转换为DO
     *
     * @param updateDTO 更新DTO
     * @return DO对象
     */
    @Mapping(source = "wkt", target = "wkt", qualifiedByName = "stringToLineString")
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SailRouteDO fromUpdateDTO(SailRouteUpdateDTO updateDTO);

    /**
     * 根据类型编码获取类型名称
     *
     * @param typeCode 类型编码
     * @return 类型名称
     */
    @Named("getTypeName")
    default String getTypeName(String typeCode) {
        return RouteCategoryEnum.getDescByCode(typeCode);
    }

    /**
     * LineString转换为WKT字符串
     *
     * @param lineString LineString对象
     * @return WKT字符串
     */
    @Named("lineStringToString")
    default String lineStringToString(LineString lineString) {
        return WktUtil.toWkt(lineString);
    }

    /**
     * WKT字符串转换为LineString
     *
     * @param wktString WKT字符串
     * @return LineString对象
     */
    @Named("stringToLineString")
    default LineString stringToLineString(String wktString) {
        Geometry geometry = WktUtil.toGeometry(wktString);
        if (geometry instanceof LineString) {
            return (LineString) geometry;
        } else {
            throw new RuntimeException("WKT字符串不是LineString类型: " + wktString);
        }
    }
}
