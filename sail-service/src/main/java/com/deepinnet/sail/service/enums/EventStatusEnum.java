package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件状态枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum EventStatusEnum {

    /**
     * 待处理
     */
    PENDING("pending", "待处理"),

    /**
     * 处理中
     */
    PROCESSING("processing", "处理中"),

    /**
     * 已办结
     */
    COMPLETED("completed", "已办结"),

    /**
     * 已关闭
     */
    CLOSED("closed", "已关闭");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static EventStatusEnum getByCode(String code) {
        for (EventStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
