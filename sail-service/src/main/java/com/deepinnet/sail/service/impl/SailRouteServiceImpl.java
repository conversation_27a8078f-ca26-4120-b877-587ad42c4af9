package com.deepinnet.sail.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.common.util.BizNoGenerateUtil;
import com.deepinnet.sail.dal.dataobject.SailRouteDO;
import com.deepinnet.sail.service.SailRouteService;
import com.deepinnet.sail.service.convert.SailRouteConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.enums.RouteCategoryEnum;
import com.deepinnet.sail.service.repository.SailRouteRepository;
import com.deepinnet.sail.service.vo.*;
import com.deepinnet.tenant.TenantIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线路服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SailRouteServiceImpl implements SailRouteService {

    private final SailRouteRepository sailRouteRepository;

    private final SailRouteConvert sailRouteConvert;

    @Override
    public List<SailRouteCategoryVO> getRouteCategoryList(SailRouteCategoryQueryDTO queryDTO) {
        // 查询所有用户自定义线路
        List<SailRouteDO> allRoutes = sailRouteRepository.findUserDefinedRoutesOrderByCreateTimeDesc();

        // 转换为VO列表
        List<SailRouteListVO> routeVOList = sailRouteConvert.toListVOList(allRoutes);

        // 按类型分组
        Map<String, List<SailRouteListVO>> typeMap = routeVOList.stream()
                .collect(Collectors.groupingBy(SailRouteListVO::getType));

        // 构建类型列表
        List<SailRouteCategoryVO> categoryList = new ArrayList<>();
        for (RouteCategoryEnum categoryEnum : RouteCategoryEnum.values()) {
            SailRouteCategoryVO categoryVO = new SailRouteCategoryVO();
            categoryVO.setCode(categoryEnum.getCode());
            categoryVO.setName(categoryEnum.getDesc());
            categoryVO.setRoutes(typeMap.getOrDefault(categoryEnum.getCode(), new ArrayList<>()));
            categoryList.add(categoryVO);
        }

        return categoryList;
    }

    @Override
    public List<SailRouteListVO> getRouteList(SailRouteListQueryDTO queryDTO) {
        List<SailRouteDO> sailRouteDOList;

        // 根据查询条件获取数据
        if (StringUtils.isNotBlank(queryDTO.getType()) && StringUtils.isNotBlank(queryDTO.getName())) {
            // 按类型和名称查询
            sailRouteDOList = sailRouteRepository.findUserDefinedRoutesByCategoryAndNameLikeOrderByCreateTimeDesc(
                    queryDTO.getType(), queryDTO.getName());
        } else if (StringUtils.isNotBlank(queryDTO.getType())) {
            // 按类型查询
            sailRouteDOList = sailRouteRepository.findUserDefinedRoutesByCategoryOrderByCreateTimeDesc(queryDTO.getType());
        } else if (StringUtils.isNotBlank(queryDTO.getName())) {
            // 按名称查询
            sailRouteDOList = sailRouteRepository.findUserDefinedRoutesByNameLikeOrderByCreateTimeDesc(queryDTO.getName());
        } else {
            // 查询所有
            sailRouteDOList = sailRouteRepository.findUserDefinedRoutesOrderByCreateTimeDesc();
        }

        return sailRouteConvert.toListVOList(sailRouteDOList);
    }

    @Override
    public SailRouteDetailVO getRouteDetail(SailRouteDetailQueryDTO queryDTO) {
        SailRouteDO sailRouteDO = sailRouteRepository.findUserDefinedRouteById(queryDTO.getId());
        if (sailRouteDO == null) {
            throw new BizException(BizErrorCode.ROUTE_NOT_FOUND.getCode(), BizErrorCode.ROUTE_NOT_FOUND.getDesc());
        }

        return sailRouteConvert.toDetailVO(sailRouteDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRoute(SailRouteCreateDTO createDTO) {
        // 验证线路类型
        if (!RouteCategoryEnum.isValidCode(createDTO.getType())) {
            throw new BizException(BizErrorCode.ROUTE_TYPE_INVALID.getCode(), BizErrorCode.ROUTE_TYPE_INVALID.getDesc());
        }

        // 检查线路名称是否已存在
        if (sailRouteRepository.existsByNameAndIdNot(createDTO.getName(), null)) {
            throw new BizException(BizErrorCode.ROUTE_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.ROUTE_NAME_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO对象
        SailRouteDO sailRouteDO = sailRouteConvert.fromCreateDTO(createDTO);

        // 生成线路编码
        sailRouteDO.setCode(BizNoGenerateUtil.generateSailRouteCode());

        // 设置创建时间和租户ID
        LocalDateTime now = LocalDateTime.now();
        sailRouteDO.setGmtCreated(now);
        sailRouteDO.setGmtModified(now);
        sailRouteDO.setTenantId(TenantIdUtil.getTenantId());

        // 保存到数据库
        sailRouteRepository.save(sailRouteDO);

        log.info("创建线路成功，线路ID：{}，线路名称：{}", sailRouteDO.getId(), sailRouteDO.getName());
        return sailRouteDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRoute(SailRouteUpdateDTO updateDTO) {
        // 验证线路类型
        if (!RouteCategoryEnum.isValidCode(updateDTO.getType())) {
            throw new BizException(BizErrorCode.ROUTE_TYPE_INVALID.getCode(), BizErrorCode.ROUTE_TYPE_INVALID.getDesc());
        }

        // 检查线路是否存在
        SailRouteDO existingRoute = sailRouteRepository.findUserDefinedRouteById(updateDTO.getId());
        if (existingRoute == null) {
            throw new BizException(BizErrorCode.ROUTE_NOT_FOUND.getCode(), BizErrorCode.ROUTE_NOT_FOUND.getDesc());
        }

        // 检查线路名称是否已存在（排除当前线路）
        if (sailRouteRepository.existsByNameAndIdNot(updateDTO.getName(), updateDTO.getId())) {
            throw new BizException(BizErrorCode.ROUTE_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.ROUTE_NAME_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO对象并设置需要更新的字段
        SailRouteDO sailRouteDO = sailRouteConvert.fromUpdateDTO(updateDTO);
        sailRouteDO.setCode(existingRoute.getCode());
        sailRouteDO.setTenantId(existingRoute.getTenantId());
        sailRouteDO.setIsDeleted(existingRoute.getIsDeleted());
        sailRouteDO.setSource(existingRoute.getSource());
        sailRouteDO.setGmtCreated(existingRoute.getGmtCreated());
        sailRouteDO.setGmtModified(LocalDateTime.now());

        // 更新数据库
        boolean success = sailRouteRepository.updateById(sailRouteDO);

        if (success) {
            log.info("更新线路成功，线路ID：{}，线路名称：{}", sailRouteDO.getId(), sailRouteDO.getName());
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRoute(Long id) {
        // 检查线路是否存在
        SailRouteDO existingRoute = sailRouteRepository.findUserDefinedRouteById(id);
        if (existingRoute == null) {
            throw new BizException(BizErrorCode.ROUTE_NOT_FOUND.getCode(), BizErrorCode.ROUTE_NOT_FOUND.getDesc());
        }

        // 逻辑删除
        existingRoute.setIsDeleted(true);
        existingRoute.setGmtModified(LocalDateTime.now());

        boolean success = sailRouteRepository.updateById(existingRoute);

        if (success) {
            log.info("删除线路成功，线路ID：{}，线路名称：{}", existingRoute.getId(), existingRoute.getName());
        }

        return success;
    }
}
