package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 出海记录查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Data
@ApiModel(value = "出海记录查询DTO")
public class SailingRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "船舶名称", example = "渔船001", notes = "支持模糊查询")
    private String shipName;

    @ApiModelProperty(value = "船舶code")
    private String shipNo;

    @ApiModelProperty(value = "所属港口", example = "西湾港", notes = "精确匹配")
    private String portName;

    @ApiModelProperty(value = "出港日期开始", example = "2025-07-01T00:00:00", notes = "查询范围的开始时间，格式：yyyy-MM-ddTHH:mm:ss")
    private LocalDateTime departureStartDate;

    @ApiModelProperty(value = "出港日期结束", example = "2025-07-31T23:59:59", notes = "查询范围的结束时间，格式：yyyy-MM-ddTHH:mm:ss")
    private LocalDateTime departureEndDate;

    @ApiModelProperty(value = "归港状态", example = "SAILING", notes = "SAILING-未归，RETURNED-已归")
    private String returnStatus;

    @ApiModelProperty(value = "预警类型", example = "sea_area_deviation", notes = "risk_time_sailing-风险时段出海，sea_area_deviation-海域偏离，forbidden_area-前往禁海区，overdue_return-到时未归，daily_not_return-当日未归，remote_not_return-异地未归")
    private String warningType;

    @ApiModelProperty(value = "页码", example = "1", notes = "从1开始", required = true)
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10", notes = "建议范围：1-100", required = true)
    private Integer pageSize = 10;
}
