package com.deepinnet.sail.service.impl;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.common.util.BizNoGenerateUtil;
import com.deepinnet.sail.dal.dataobject.SailPointDO;
import com.deepinnet.sail.service.SailPointService;
import com.deepinnet.sail.service.convert.SailPointConvert;
import com.deepinnet.sail.service.dto.SailPointCategoryQueryDTO;
import com.deepinnet.sail.service.dto.SailPointCreateDTO;
import com.deepinnet.sail.service.dto.SailPointDetailQueryDTO;
import com.deepinnet.sail.service.dto.SailPointListQueryDTO;
import com.deepinnet.sail.service.dto.SailPointUpdateDTO;
import com.deepinnet.sail.service.enums.PointCategoryEnum;
import com.deepinnet.sail.service.repository.SailPointRepository;
import com.deepinnet.sail.service.vo.SailPointCategoryVO;
import com.deepinnet.sail.service.vo.SailPointDetailVO;
import com.deepinnet.sail.service.vo.SailPointListVO;
import com.deepinnet.tenant.TenantIdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 点位服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class SailPointServiceImpl implements SailPointService {

    @Autowired
    private SailPointRepository sailPointRepository;

    @Autowired
    private SailPointConvert sailPointConvert;

    @Override
    public List<SailPointCategoryVO> getPointCategoryList(SailPointCategoryQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        // 获取所有用户定义的点位
        List<SailPointDO> pointList = sailPointRepository.findUserDefinedPointsByTenantIdOrderByCreateTimeDesc(tenantId);

        if (CollectionUtils.isEmpty(pointList)) {
            // 返回空的分类列表，但包含所有分类
            return Arrays.stream(PointCategoryEnum.values())
                    .map(categoryEnum -> SailPointCategoryVO.of(
                            categoryEnum.getCode(), 
                            categoryEnum.getDesc(), 
                            new ArrayList<>()
                    ))
                    .collect(Collectors.toList());
        }

        // 转换为VO
        List<SailPointListVO> pointVOList = sailPointConvert.toListVOList(pointList);

        // 按分类分组
        Map<String, List<SailPointListVO>> typeMap = pointVOList.stream()
                .collect(Collectors.groupingBy(SailPointListVO::getType));

        // 构建分类VO列表
        return Arrays.stream(PointCategoryEnum.values())
                .map(categoryEnum -> SailPointCategoryVO.of(
                        categoryEnum.getCode(), 
                        categoryEnum.getDesc(), 
                        typeMap.getOrDefault(categoryEnum.getCode(), new ArrayList<>())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<SailPointListVO> getPointList(SailPointListQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        List<SailPointDO> pointList;
        if (StringUtils.hasText(queryDTO.getType())) {
            pointList = sailPointRepository.findUserDefinedPointsByTenantIdAndTypeOrderByCreateTimeDesc(tenantId, queryDTO.getType());
        } else {
            pointList = sailPointRepository.findUserDefinedPointsByTenantIdOrderByCreateTimeDesc(tenantId);
        }

        if (CollectionUtils.isEmpty(pointList)) {
            return new ArrayList<>();
        }

        return sailPointConvert.toListVOList(pointList);
    }

    @Override
    public SailPointDetailVO getPointDetail(SailPointDetailQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        SailPointDO pointDO = sailPointRepository.getById(queryDTO.getId());
        if (pointDO == null || pointDO.getIsDeleted() || !tenantId.equals(pointDO.getTenantId())) {
            throw new BizException(BizErrorCode.POINT_NOT_FOUND.getCode(), BizErrorCode.POINT_NOT_FOUND.getDesc());
        }

        return sailPointConvert.toDetailVO(pointDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPoint(SailPointCreateDTO createDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        // 验证点位类型
        if (!PointCategoryEnum.isValidCode(createDTO.getType())) {
            throw new BizException(BizErrorCode.POINT_TYPE_INVALID.getCode(), BizErrorCode.POINT_TYPE_INVALID.getDesc());
        }

        // 验证点位名称唯一性
        if (sailPointRepository.existsByNameAndIdNot(createDTO.getName(), null)) {
            throw new BizException(BizErrorCode.POINT_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.POINT_NAME_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO
        SailPointDO pointDO = sailPointConvert.fromCreateDTO(createDTO);
        pointDO.setCode(BizNoGenerateUtil.generateSailPointCode());
        pointDO.setTenantId(tenantId);
        
        LocalDateTime now = LocalDateTime.now();
        pointDO.setGmtCreated(now);
        pointDO.setGmtModified(now);

        // 保存并返回ID
        sailPointRepository.save(pointDO);
        return pointDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updatePoint(SailPointUpdateDTO updateDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        // 检查点位是否存在
        SailPointDO existingPoint = sailPointRepository.getById(updateDTO.getId());
        if (existingPoint == null || existingPoint.getIsDeleted() || !tenantId.equals(existingPoint.getTenantId())) {
            throw new BizException(BizErrorCode.POINT_NOT_FOUND.getCode(), BizErrorCode.POINT_NOT_FOUND.getDesc());
        }

        // 验证点位类型
        if (!PointCategoryEnum.isValidCode(updateDTO.getType())) {
            throw new BizException(BizErrorCode.POINT_TYPE_INVALID.getCode(), BizErrorCode.POINT_TYPE_INVALID.getDesc());
        }

        // 验证点位名称唯一性（排除自己）
        if (sailPointRepository.existsByNameAndIdNot(updateDTO.getName(), updateDTO.getId())) {
            throw new BizException(BizErrorCode.POINT_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.POINT_NAME_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO并更新
        SailPointDO pointDO = sailPointConvert.fromUpdateDTO(updateDTO);
        pointDO.setCode(existingPoint.getCode());
        pointDO.setTenantId(tenantId);
        pointDO.setIsDeleted(false);
        pointDO.setSource("userDefined");
        pointDO.setGmtCreated(existingPoint.getGmtCreated());
        pointDO.setGmtModified(LocalDateTime.now());

        sailPointRepository.updateById(pointDO);
        return pointDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePoint(SailPointDetailQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();

        SailPointDO pointDO = sailPointRepository.getById(queryDTO.getId());
        if (pointDO == null || pointDO.getIsDeleted() || !tenantId.equals(pointDO.getTenantId())) {
            throw new BizException(BizErrorCode.POINT_NOT_FOUND.getCode(), BizErrorCode.POINT_NOT_FOUND.getDesc());
        }

        // 逻辑删除
        sailPointRepository.removeById(queryDTO.getId());
    }


}
