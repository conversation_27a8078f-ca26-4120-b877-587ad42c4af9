package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SailEventDO;
import com.deepinnet.sail.dal.dto.EventTypeStatisticsDTO;
import com.deepinnet.sail.dal.mapper.SailEventMapper;
import com.deepinnet.sail.service.repository.SailEventRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件信息Repository实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Service
public class SailEventRepositoryImpl extends ServiceImpl<SailEventMapper, SailEventDO> implements SailEventRepository {

    @Override
    public SailEventDO getByEventNo(String eventNo) {
        return super.getOne(Wrappers.lambdaQuery(SailEventDO.class)
                .eq(SailEventDO::getEventNo, eventNo));
    }

    @Override
    public Boolean removeByEventNo(String eventNo) {
        return super.remove(Wrappers.lambdaQuery(SailEventDO.class)
                .eq(SailEventDO::getEventNo, eventNo));
    }

    @Override
    public List<EventTypeStatisticsDTO> getEventTypeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getEventTypeStatistics(startTime, endTime);
    }
}
