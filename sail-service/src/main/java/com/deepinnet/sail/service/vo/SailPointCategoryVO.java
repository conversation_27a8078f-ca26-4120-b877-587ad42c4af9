package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 点位分类VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "点位分类VO", description = "点位分类信息")
public class SailPointCategoryVO {

    @ApiModelProperty(value = "分类编码", example = "port_terminal")
    private String type;

    @ApiModelProperty(value = "分类名称", example = "港口码头")
    private String typeName;

    @ApiModelProperty(value = "该分类下的点位列表")
    private List<SailPointListVO> pointList;

    /**
     * 创建点位分类VO的静态方法
     */
    public static SailPointCategoryVO of(String type, String typeName, List<SailPointListVO> pointList) {
        SailPointCategoryVO vo = new SailPointCategoryVO();
        vo.type = type;
        vo.typeName = typeName;
        vo.pointList = pointList;
        return vo;
    }
}

