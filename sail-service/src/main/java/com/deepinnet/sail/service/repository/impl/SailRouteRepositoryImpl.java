package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SailRouteDO;
import com.deepinnet.sail.dal.mapper.SailRouteMapper;
import com.deepinnet.sail.service.repository.SailRouteRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 线路表 Repository 实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class SailRouteRepositoryImpl extends ServiceImpl<SailRouteMapper, SailRouteDO>
        implements SailRouteRepository {

    private static final String USER_DEFINED_SOURCE = "userDefined";

    @Override
    public List<SailRouteDO> findUserDefinedRoutesOrderByCreateTimeDesc() {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false)
                .orderByDesc(SailRouteDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailRouteDO> findUserDefinedRoutesByCategoryOrderByCreateTimeDesc(String category) {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false)
                .eq(SailRouteDO::getType, category)
                .orderByDesc(SailRouteDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailRouteDO> findUserDefinedRoutesByNameLikeOrderByCreateTimeDesc(String name) {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false)
                .like(StringUtils.isNotBlank(name), SailRouteDO::getName, name)
                .orderByDesc(SailRouteDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailRouteDO> findUserDefinedRoutesByCategoryAndNameLikeOrderByCreateTimeDesc(String category, String name) {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false)
                .eq(StringUtils.isNotBlank(category), SailRouteDO::getType, category)
                .like(StringUtils.isNotBlank(name), SailRouteDO::getName, name)
                .orderByDesc(SailRouteDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long excludeId) {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false)
                .eq(SailRouteDO::getName, name);

        if (excludeId != null) {
            wrapper.ne(SailRouteDO::getId, excludeId);
        }

        return super.count(wrapper) > 0;
    }

    @Override
    public SailRouteDO findUserDefinedRouteById(Long id) {
        LambdaQueryWrapper<SailRouteDO> wrapper = Wrappers.lambdaQuery(SailRouteDO.class)
                .eq(SailRouteDO::getId, id)
                .eq(SailRouteDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailRouteDO::getIsDeleted, false);

        return super.getOne(wrapper);
    }
}
