package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.PeopleFlowVitalityIndexDO;
import com.deepinnet.sail.service.dto.PeopleFlowVitalityIndexDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 人流活力指数转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Mapper(componentModel = "spring")
public interface PeopleFlowVitalityIndexConvert {

    /**
     * DO转换为VO
     *
     * @param peopleFlowVitalityIndexDO DO对象
     * @return VO对象
     */
    PeopleFlowVitalityIndexDTO toDTO(PeopleFlowVitalityIndexDO peopleFlowVitalityIndexDO);

    /**
     * DO列表转换为VO列表
     *
     * @param peopleFlowVitalityIndexDOList DO列表
     * @return VO列表
     */
    List<PeopleFlowVitalityIndexDTO> toDTOList(List<PeopleFlowVitalityIndexDO> peopleFlowVitalityIndexDOList);
}