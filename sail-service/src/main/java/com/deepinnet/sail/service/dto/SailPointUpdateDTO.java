package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 点位编辑DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "点位编辑DTO", description = "编辑点位的请求参数")
public class SailPointUpdateDTO {

    @ApiModelProperty(value = "点位ID", example = "1", required = true)
    @NotNull(message = "点位ID不能为空")
    private Long id;

    @ApiModelProperty(value = "点位类型", example = "port_terminal", 
            notes = "可能值：port_terminal(港口码头), anchorage_point(锚泊点), lighthouse(灯塔), beacon(信标), monitoring_point(监控点), rescue_station(救援站), fuel_station(加油站), checkpoint(检查点), danger_point(危险点), other(其他)", required = true)
    @NotBlank(message = "点位类型不能为空")
    private String type;

    @ApiModelProperty(value = "点位名称", example = "深圳港1号码头", notes = "手动输入，最大长度：20", required = true)
    @NotBlank(message = "点位名称不能为空")
    @Size(max = 20, message = "点位名称长度不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "点位地址", example = "深圳市南山区蛇口港", notes = "手动输入，最大长度：100", required = true)
    @NotBlank(message = "点位地址不能为空")
    @Size(max = 100, message = "点位地址长度不能超过100个字符")
    private String address;

    @ApiModelProperty(value = "点位坐标", example = "POINT(114.057868 22.543099)", 
            notes = "WKT格式的点位坐标信息", required = true)
    @NotBlank(message = "点位坐标不能为空")
    private String wkt;
}

