package com.deepinnet.sail.service.enums;

import lombok.*;

/**
 * 海域类型枚举
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum SeaAreaTypeEnum {

    /**
     * 禁海区
     */
    FORBIDDEN("forbidden", "禁海区"),

    /**
     * 普通海域
     */
    NORMAL("normal", "普通海域"),

    /**
     * 港口
     */
    PORT("port", "港口"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static SeaAreaTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (SeaAreaTypeEnum typeEnum : SeaAreaTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述，如果不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        SeaAreaTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getDesc() : "";
    }
}
