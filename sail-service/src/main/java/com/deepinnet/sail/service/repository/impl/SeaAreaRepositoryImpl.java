package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SeaAreaDO;
import com.deepinnet.sail.dal.mapper.SeaAreaMapper;
import com.deepinnet.sail.service.repository.SeaAreaRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 海域表 Repository 实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Service
public class SeaAreaRepositoryImpl extends ServiceImpl<SeaAreaMapper, SeaAreaDO>
        implements SeaAreaRepository {

    private static final String USER_DEFINED_SOURCE = "userDefined";

    @Override
    public List<SeaAreaDO> findByType(String type) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getType, type)
                .orderBy(true, true, SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SeaAreaDO> findUserDefinedAreasOrderByCreateTimeDesc() {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .orderByDesc(SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SeaAreaDO> findUserDefinedAreasByCategoryOrderByCreateTimeDesc(String category) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .eq(SeaAreaDO::getType, category)
                .orderByDesc(SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SeaAreaDO> findUserDefinedAreasByNameLikeOrderByCreateTimeDesc(String name) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .like(StringUtils.isNotBlank(name), SeaAreaDO::getName, name)
                .orderByDesc(SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SeaAreaDO> findUserDefinedAreasByCategoryAndNameLikeOrderByCreateTimeDesc(String category, String name) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .eq(StringUtils.isNotBlank(category), SeaAreaDO::getType, category)
                .like(StringUtils.isNotBlank(name), SeaAreaDO::getName, name)
                .orderByDesc(SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long excludeId) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .eq(SeaAreaDO::getName, name);

        if (excludeId != null) {
            wrapper.ne(SeaAreaDO::getId, excludeId);
        }

        return super.count(wrapper) > 0;
    }

    @Override
    public SeaAreaDO findUserDefinedAreaById(Long id) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getId, id)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false);

        return super.getOne(wrapper);
    }

    @Override
    public List<SeaAreaDO> findUserDefinedAreasByAreaCodeOrderByCreateTimeDesc(String areaCode) {
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .eq(StringUtils.isNotBlank(areaCode), SeaAreaDO::getAreaCode, areaCode)
                .orderByDesc(SeaAreaDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public boolean existsByAreaCodeAndIdNot(String areaCode, Long excludeId) {
        if (StringUtils.isBlank(areaCode)) {
            return false; // 空的areaCode不检查唯一性
        }
        
        LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, USER_DEFINED_SOURCE)
                .eq(SeaAreaDO::getIsDeleted, false)
                .eq(SeaAreaDO::getAreaCode, areaCode);

        if (excludeId != null) {
            wrapper.ne(SeaAreaDO::getId, excludeId);
        }

        return super.count(wrapper) > 0;
    }
}