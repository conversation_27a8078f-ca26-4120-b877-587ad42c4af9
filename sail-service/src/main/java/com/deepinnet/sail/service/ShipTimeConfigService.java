package com.deepinnet.sail.service;

import java.time.*;

/**
 * <AUTHOR> wong
 * @create 2025/8/18 10:49
 * @Description
 *
 */
public interface ShipTimeConfigService {

    /**
     * 是否是常规风险时段
     *
     * @param checkTime 检查时间
     * @return true-是风险时段，false-不是风险时段
     */
    boolean isRegularRiskTime(LocalDateTime checkTime);

    /**
     * 是否是自定义风险时段
     *
     * @param checkTime 检查时间
     * @return true-是风险时段，false-不是风险时段
     */
    boolean isCustomRiskTime(LocalDateTime checkTime);

    /**
     * 是否按时归港（是否超过最迟归港时间）
     *
     * @param returnTime 归港时间
     * @return true-按时归港，false-超过最迟归港时间
     */
    boolean isOnTimeReturn(LocalDateTime returnTime);

    /**
     * 获取最迟归港时间
     *
     * @return 最迟归港时间
     */
    LocalTime getMaxReturnTime();


    /**
     * 保存配置
     *
     * @param configType  配置类型
     * @param configValue 配置值
     */
    void saveConfig(String configType, String configValue);
}
