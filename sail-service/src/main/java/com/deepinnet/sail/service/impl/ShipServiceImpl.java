package com.deepinnet.sail.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.sail.dal.dataobject.ShipDO;
import com.deepinnet.sail.dal.dataobject.ShipPositionDO;
import com.deepinnet.sail.service.ShipService;
import com.deepinnet.sail.service.convert.ShipConvert;
import com.deepinnet.sail.service.convert.ShipPositionConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.repository.ShipPositionRepository;
import com.deepinnet.sail.service.repository.ShipRepository;
import com.deepinnet.sail.service.vo.ShipDetailVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 船舶服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Service
@RequiredArgsConstructor
public class ShipServiceImpl implements ShipService {

    private final ShipRepository shipRepository;
    private final ShipPositionRepository shipPositionRepository;
    private final ShipConvert shipConvert;
    private final ShipPositionConvert shipPositionConvert;

    @Override
    public List<ShipDetailVO> getAllShipDetail(ShipDetailQueryDTO queryDTO) {
        // 查询所有船舶详细信息
        List<ShipDO> shipList = shipRepository.list(Wrappers.lambdaQuery(ShipDO.class)
                .eq(ShipDO::getPortCode, queryDTO.getPortCode()));

        if (CollUtil.isEmpty(shipList)) {
            return null;
        }

        return shipConvert.toDetailVOList(shipList);
    }

    @Override
    public List<ShipPositionDTO> getAllShipLatestPosition(ShipPositionQueryDTO queryDTO) {
        // 计算1小时前的时间戳
        long oneHourAgo = System.currentTimeMillis() - 60 * 60 * 1000;

        // 不传入则查询全部
        List<String> shipNos = queryDTO.getShipNos();
        if (CollUtil.isEmpty(shipNos)) {
            List<ShipDO> shipList = shipRepository.list(Wrappers.lambdaQuery(ShipDO.class)
                    .in(ShipDO::getPortCode, queryDTO.getPortCode()));
            if (CollUtil.isEmpty(shipList)) {
                return null;
            }

            shipNos = shipList.stream().map(ShipDO::getShipNo)
                    .collect(Collectors.toList());
        }

        // 查询船舶实时位置（1小时内数据）
        List<ShipPositionDO> positionList = shipPositionRepository.getLatestPositionsByShipNosWithTimeLimit(
                shipNos, oneHourAgo);

        if (CollUtil.isEmpty(positionList)) {
            return Collections.emptyList();
        }

        return shipPositionConvert.toDTOList(positionList);
    }
}
