package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;

import java.util.List;

/**
 * 海域服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
public interface SeaAreaService {

    /**
     * 根据类型查询海域/港口列表
     *
     * @param queryDTO 查询条件
     * @return 海域列表
     */
    List<SeaAreaDTO> getSeaAreasByType(SeaAreaQueryDTO queryDTO);

    /**
     * 获取区域分类列表（包含每个分类下的区域列表）
     *
     * @param queryDTO 查询参数（当前为空，预留扩展）
     * @return 区域分类列表
     */
    List<SeaAreaCategoryVO> getAreaCategoryList(SeaAreaCategoryQueryDTO queryDTO);

    /**
     * 根据条件查询区域列表
     *
     * @param queryDTO 查询条件
     * @return 区域列表
     */
    List<SeaAreaListVO> getAreaList(SeaAreaListQueryDTO queryDTO);

    /**
     * 根据ID获取区域详情
     *
     * @param queryDTO 查询条件
     * @return 区域详情
     */
    SeaAreaDetailVO getAreaDetail(SeaAreaDetailQueryDTO queryDTO);

    /**
     * 新增区域
     *
     * @param createDTO 创建参数
     * @return 区域ID
     */
    Long createArea(SeaAreaCreateDTO createDTO);

    /**
     * 编辑区域
     *
     * @param updateDTO 更新参数
     * @return 是否成功
     */
    Boolean updateArea(SeaAreaUpdateDTO updateDTO);

    /**
     * 删除区域
     *
     * @param id 区域ID
     * @return 是否成功
     */
    Boolean deleteArea(Long id);
}
