package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 人流洞察查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Data
@ApiModel(value = "人流洞察查询DTO", description = "人流洞察数据查询参数")
public class PeopleFlowInsightQueryDTO {

    @ApiModelProperty(value = "洞察维度.可选值：u_sex（性别）、u_occupation（职业）、u_age（年龄）、u_permanent_adcode（常驻区域），property_level-资产分布")
    @NotBlank(message = "洞察维度不能为空")
    private String profile;

    @ApiModelProperty(value = "区域code")
    @NotBlank(message = "区域code不能为空")
    private String areaCode;
}