package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.SeaAreaFlowSummaryDO;
import com.deepinnet.sail.service.vo.SeaAreaFlowSummaryVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 海域人流统计转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Mapper(componentModel = "spring")
public interface SeaAreaFlowStatisticsConvert {

    /**
     * DO转换为VO
     *
     * @param seaAreaFlowSummaryDO DO对象
     * @return VO对象
     */
    SeaAreaFlowSummaryVO toVO(SeaAreaFlowSummaryDO seaAreaFlowSummaryDO);

    /**
     * DO列表转换为VO列表
     *
     * @param seaAreaFlowSummaryDOList DO列表
     * @return VO列表
     */
    List<SeaAreaFlowSummaryVO> toVOList(List<SeaAreaFlowSummaryDO> seaAreaFlowSummaryDOList);
}