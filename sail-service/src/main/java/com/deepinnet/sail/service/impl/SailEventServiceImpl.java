package com.deepinnet.sail.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.common.constants.DownloadSetConstants;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.common.util.*;
import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.dal.dto.EventTypeStatisticsDTO;
import com.deepinnet.sail.service.SailEventService;
import com.deepinnet.sail.service.convert.EventConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.enums.*;
import com.deepinnet.sail.service.excel.SailEventExportRow;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.sail.service.vo.*;
import com.github.pagehelper.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 事件服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Service
@RequiredArgsConstructor
public class SailEventServiceImpl implements SailEventService {

    private final SailEventRepository sailEventRepository;

    private final SailEventProcessRepository sailEventProcessRepository;

    private final EventConvert eventConvert;

    private final TransactionTemplate transactionTemplate;

    @Override
    public CommonPage<EventVO> pageEvents(EventQueryDTO queryDTO) {
        // 使用PageHelper进行分页
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        List<SailEventDO> eventList = getSailEventList(queryDTO);

        // 转换为VO
        List<EventVO> voList = eventList.stream()
                .map(eventConvert::toVO)
                .collect(Collectors.toList());

        // 构建CommonPage返回结果
        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), voList);
    }

    @Override
    public EventVO getEventDetail(String eventNo) {
        SailEventDO sailEventDO = sailEventRepository.getByEventNo(eventNo);
        if (sailEventDO == null) {
            return null;
        }

        List<EventProcessVO> eventProcessRecords = getEventProcessRecords(sailEventDO.getEventNo());
        EventVO eventVO = eventConvert.toVO(sailEventDO);
        eventVO.setProcessEventDTOList(eventProcessRecords);

        if (StrUtil.equals(eventVO.getStatus(), EventStatusEnum.COMPLETED.getCode())) {
            eventVO.setCompleteTime(eventProcessRecords.get(eventProcessRecords.size() - 1).getProcessTime());
        }

        return eventVO;
    }

    @Override
    public String reportEvent(EventReportDTO reportDTO) {
        SailEventDO sailEventDO = buildEvent(reportDTO);
        sailEventRepository.save(sailEventDO);
        return sailEventDO.getEventNo();
    }


    @Override
    public Long processEvent(EventProcessDTO processDTO) {
        String eventNo = processDTO.getEventNo();

        // 查询事件信息
        SailEventDO sailEventDO = sailEventRepository.getByEventNo(eventNo);
        if (sailEventDO == null) {
            throw new BizException(BizErrorCode.EVENT_NOT_FOUND.getCode(), BizErrorCode.EVENT_NOT_FOUND.getDesc());
        }

        String currentStatus = sailEventDO.getStatus();

        // 检查事件状态是否可以处理
        if (!StrUtil.equals(EventStatusEnum.PENDING.getCode(), currentStatus) && !StrUtil.equals(EventStatusEnum.PROCESSING.getCode(), currentStatus)) {
            throw new BizException(BizErrorCode.PROCESS_INVALID_EVENT_STATUS.getCode(), BizErrorCode.PROCESS_INVALID_EVENT_STATUS.getDesc());
        }

        // 根据处理阶段确定新状态
        String newStatus = determineNewStatus(processDTO.getProcessStage());

        // 创建处理记录
        SailEventProcessDO processDO = buildEventProcessRecord(sailEventDO, processDTO);

        transactionTemplate.executeWithoutResult(action -> {
            // 保存处理记录
            sailEventProcessRepository.save(processDO);

            // 更新事件状态
            sailEventDO.setStatus(newStatus);
            sailEventDO.setGmtModified(LocalDateTime.now());
            sailEventRepository.updateById(sailEventDO);
        });

        return processDO.getId();
    }

    @Override
    public Boolean deleteEvent(String eventNo) {
        return sailEventRepository.removeByEventNo(eventNo);
    }

    @Override
    public void exportEvents(EventQueryDTO queryDTO, HttpServletResponse response) {
        List<SailEventDO> sailEventList = getSailEventList(queryDTO);
        if (CollUtil.isEmpty(sailEventList)) {
            handleEmptyData(response);
            return;
        }

        List<SailEventExportRow> exportRows = sailEventList.stream()
                .map(this::convertToExportRow)
                .collect(Collectors.toList());

        String fileName = generateFileName();
        setDownloadResponseHeaders(response, fileName);

        try {
            EasyExcel.write(response.getOutputStream(), SailEventExportRow.class)
                    .sheet("业务数据明细")
                    .doWrite(exportRows);
        } catch (IOException e) {
            LogUtil.error("数据下载异常, 查询参数: {}", JSONObject.toJSONString(queryDTO), e);
            throw new BizException(BizErrorCode.FILE_DOWNLOAD_ERROR.getCode(), BizErrorCode.FILE_DOWNLOAD_ERROR.getDesc());
        }
    }

    @Override
    public EventDashboardVO pageQueryDashboardEvents(EventDashboardQueryDTO requestDTO) {
        EventDashboardVO dashboardVO = new EventDashboardVO();

        // 获取事件类型统计
        List<EventTypeStatisticsVO> typeStatistics = getEventTypeStatistics(requestDTO);
        dashboardVO.setTypeStatistics(typeStatistics);

        // 获取事件分页列表
        CommonPage<EventVO> eventList = pageDashboardEvents(requestDTO);
        dashboardVO.setEventList(eventList);

        return dashboardVO;
    }

    private void handleEmptyData(HttpServletResponse response) {
        try {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType(DownloadSetConstants.APPLICATION_JSON_CONTENT_TYPE);
            String errorResponse = JSONObject.toJSONString(
                    Result.fail(BizErrorCode.NOT_EXIST_ERROR.getCode(), BizErrorCode.NOT_EXIST_ERROR.getDesc()));
            response.getWriter().write(errorResponse);
        } catch (IOException e) {
            LogUtil.error("空数据处理异常", e);
            throw new BizException(BizErrorCode.FILE_DOWNLOAD_ERROR.getCode(), BizErrorCode.FILE_DOWNLOAD_ERROR.getDesc());
        }
    }

    private void setDownloadResponseHeaders(HttpServletResponse response, String fileName) {
        response.setContentType(DownloadSetConstants.EXCEL_CONTENT_TYPE);
        response.setCharacterEncoding(DownloadSetConstants.CHARACTER_ENCODING);
        response.setHeader(DownloadSetConstants.EXCEL_EXPORT_HEADER_NAME
                , DownloadSetConstants.EXCEL_EXPORT_HEADER_VALUE
                        + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
    }

    private List<SailEventDO> getSailEventList(EventQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<SailEventDO> wrapper = new LambdaQueryWrapper<>();

        // 事件描述模糊查询
        if (StringUtils.hasText(queryDTO.getEventDescription())) {
            wrapper.like(SailEventDO::getEventDescription, queryDTO.getEventDescription());
        }

        // 事件来源精准查询
        if (StringUtils.hasText(queryDTO.getEventSource())) {
            wrapper.eq(SailEventDO::getEventSource, queryDTO.getEventSource());
        }

        // 紧急程度精准查询
        if (StringUtils.hasText(queryDTO.getUrgencyLevel())) {
            wrapper.eq(SailEventDO::getUrgencyLevel, queryDTO.getUrgencyLevel());
        }

        // 状态精准查询
        if (StringUtils.hasText(queryDTO.getStatus())) {
            wrapper.eq(SailEventDO::getStatus, queryDTO.getStatus());
        }

        // 按事发时间倒序排序
        wrapper.orderByDesc(SailEventDO::getIncidentTime);

        // 执行查询
        return sailEventRepository.list(wrapper);
    }

    private SailEventExportRow convertToExportRow(SailEventDO eventDO) {
        SailEventExportRow exportRow = new SailEventExportRow();
        exportRow.setEventNo(eventDO.getEventNo());

        EventSourceEnum eventSourceEnum = EventSourceEnum.getByCode(eventDO.getEventSource());
        if (eventSourceEnum != null) {
            exportRow.setEventSource(eventSourceEnum.getDesc());
        }

        exportRow.setEventDescription(eventDO.getEventDescription());

        EventUrgencyEnum eventUrgencyEnum = EventUrgencyEnum.getByCode(eventDO.getUrgencyLevel());
        if (eventUrgencyEnum != null) {
            exportRow.setUrgencyLevel(eventUrgencyEnum.getDesc());
        }

        exportRow.setIncidentAddress(eventDO.getIncidentAddress());
        exportRow.setX(eventDO.getX());
        exportRow.setY(eventDO.getY());
        exportRow.setIncidentTime(eventDO.getIncidentTime());
        exportRow.setReportDepartment(eventDO.getReportDepartment());
        exportRow.setReporterName(eventDO.getReporterName());
        exportRow.setReporterPhone(eventDO.getReporterPhone());
        exportRow.setReportTime(eventDO.getReportTime());

        EventStatusEnum statusEnum = EventStatusEnum.getByCode(eventDO.getStatus());
        if (statusEnum != null) {
            exportRow.setStatus(statusEnum.getDesc());
        }

        return exportRow;
    }

    private String generateFileName() {
        return DownloadSetConstants.FILE_NAME
                + DownloadSetConstants.FILE_NAME_SUFFIX;
    }

    /**
     * 根据处理阶段确定新状态
     */
    private String determineNewStatus(String processStage) {
        switch (processStage) {
            case "process":
                return "processing";
            case "complete":
                return "completed";
            case "close":
                return "closed";
            default:
                throw new BizException(BizErrorCode.PROCESS_INVALID_PROCESS_STAGE.getCode());
        }
    }

    private SailEventDO buildEvent(EventReportDTO reportDTO) {
        SailEventDO sailEventDO = new SailEventDO();
        String eventNo = BizNoGenerateUtil.generateEventNo();
        sailEventDO.setEventNo(eventNo);

        // 设置基本信息
        sailEventDO.setEventSource(reportDTO.getEventSource());
        sailEventDO.setEventDescription(reportDTO.getEventDescription());
        sailEventDO.setUrgencyLevel(reportDTO.getUrgencyLevel());
        sailEventDO.setIncidentAddress(reportDTO.getIncidentAddress());
        sailEventDO.setX(reportDTO.getX());
        sailEventDO.setY(reportDTO.getY());
        sailEventDO.setIncidentTime(reportDTO.getIncidentTime());
        sailEventDO.setReportDepartment(reportDTO.getReportDepartment());
        sailEventDO.setReporterName(reportDTO.getReporterName());
        sailEventDO.setReporterPhone(reportDTO.getReporterPhone());
        sailEventDO.setReportTime(reportDTO.getReportTime());

        // 设置默认状态为待处理
        sailEventDO.setStatus(EventStatusEnum.PENDING.getCode());

        // 保存附件
        if (CollUtil.isNotEmpty(reportDTO.getAttachments())) {
            sailEventDO.setAttachments(JSONUtil.toJsonStr(reportDTO.getAttachments()));
        }

        // 设置时间戳
        LocalDateTime now = LocalDateTime.now();
        sailEventDO.setGmtCreated(now);
        sailEventDO.setGmtModified(now);

        return sailEventDO;
    }

    private SailEventProcessDO buildEventProcessRecord(SailEventDO sailEventDO, EventProcessDTO processDTO) {
        SailEventProcessDO processDO = new SailEventProcessDO();
        processDO.setEventNo(sailEventDO.getEventNo());
        processDO.setProcessorNo(UserUtil.getUserNo());
        processDO.setProcessorName(UserUtil.getUserName());
        List<Long> departmentIds = UserUtil.getDepartmentIds();
        if (CollUtil.isNotEmpty(departmentIds)) {
            processDO.setProcessDepartment(departmentIds.get(0));
        }
        processDO.setProcessStage(processDTO.getProcessStage());
        processDO.setProcessOpinion(processDTO.getProcessOpinion());
        processDO.setProcessTime(LocalDateTime.now());

        LocalDateTime now = LocalDateTime.now();
        processDO.setGmtCreated(now);
        processDO.setGmtModified(now);

        return processDO;
    }

    private List<EventProcessVO> getEventProcessRecords(String eventNo) {
        LambdaQueryWrapper<SailEventProcessDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SailEventProcessDO::getEventNo, eventNo)
                .orderByAsc(SailEventProcessDO::getProcessTime);

        List<SailEventProcessDO> processRecords = sailEventProcessRepository.list(wrapper);

        return processRecords.stream()
                .map(eventConvert::toProcessVO)
                .collect(Collectors.toList());
    }


    private List<EventTypeStatisticsVO> getEventTypeStatistics(EventDashboardQueryDTO requestDTO) {
        List<EventTypeStatisticsDTO> statisticsList = sailEventRepository.getEventTypeStatistics(
                requestDTO.getStartTime(), requestDTO.getEndTime());

        if (CollUtil.isEmpty(statisticsList)) {
            return CollUtil.newArrayList();
        }

        // 计算总数用于计算百分比
        long totalCount = statisticsList.stream()
                .mapToLong(EventTypeStatisticsDTO::getCount)
                .sum();

        return statisticsList.stream()
                .map(dto -> {
                    EventTypeStatisticsVO vo = new EventTypeStatisticsVO();
                    vo.setEventSource(dto.getEventSource());
                    vo.setCount(dto.getCount());

                    // 计算百分比，保留一位小数
                    if (totalCount > 0) {
                        BigDecimal percentage = BigDecimal.valueOf(dto.getCount())
                                .multiply(BigDecimal.valueOf(100))
                                .divide(BigDecimal.valueOf(totalCount), 1, RoundingMode.HALF_UP);
                        vo.setPercentage(percentage.doubleValue());
                    } else {
                        vo.setPercentage(0.0);
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    private CommonPage<EventVO> pageDashboardEvents(EventDashboardQueryDTO requestDTO) {
        // 使用PageHelper进行分页
        Page<Object> page = PageHelper.startPage(requestDTO.getPageNum(), requestDTO.getPageSize());

        List<SailEventDO> eventList = getDashboardEventList(requestDTO);

        // 转换为VO
        List<EventVO> voList = eventList.stream()
                .map(eventConvert::toVO)
                .collect(Collectors.toList());

        // 构建CommonPage返回结果
        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), voList);
    }

    /**
     * 构建大屏事件查询条件
     */
    private List<SailEventDO> getDashboardEventList(EventDashboardQueryDTO requestDTO) {
        // 构建查询条件
        LambdaQueryWrapper<SailEventDO> wrapper = new LambdaQueryWrapper<>();

        // 事件来源精准查询
        if (StringUtils.hasText(requestDTO.getEventSource())) {
            wrapper.eq(SailEventDO::getEventSource, requestDTO.getEventSource());
        }

        // 事件发生时间范围查询
        if (requestDTO.getStartTime() != null) {
            wrapper.ge(SailEventDO::getIncidentTime, requestDTO.getStartTime());
        }

        if (requestDTO.getEndTime() != null) {
            wrapper.le(SailEventDO::getIncidentTime, requestDTO.getEndTime());
        }

        // 按事发时间倒序排序
        wrapper.orderByDesc(SailEventDO::getIncidentTime);

        // 执行查询
        return sailEventRepository.list(wrapper);
    }
}
