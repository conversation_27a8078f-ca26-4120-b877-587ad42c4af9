package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 船舶位置查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-26
 */
@Data
@ApiModel(value = "船舶位置查询DTO", description = "船舶实时位置查询参数")
public class ShipPositionQueryDTO {

    @ApiModelProperty(value = "船舶编号列表")
    private List<String> shipNos;

    @ApiModelProperty(value = "港口编号")
    private String portCode;
}
