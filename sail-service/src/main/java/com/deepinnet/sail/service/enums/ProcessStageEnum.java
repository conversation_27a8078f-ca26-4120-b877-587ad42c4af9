package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理阶段枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum ProcessStageEnum {

    /**
     * 处理
     */
    PROCESS("process", "处理"),

    /**
     * 办结
     */
    COMPLETE("complete", "办结"),

    /**
     * 关闭
     */
    CLOSE("close", "关闭");

    /**
     * 阶段代码
     */
    private final String code;

    /**
     * 阶段描述
     */
    private final String desc;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static ProcessStageEnum getByCode(String code) {
        for (ProcessStageEnum stage : values()) {
            if (stage.getCode().equals(code)) {
                return stage;
            }
        }
        return null;
    }
}
