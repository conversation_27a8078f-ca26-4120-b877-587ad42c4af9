package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.ShipSailingRecordDO;
import com.deepinnet.sail.service.dto.ShipSailingRecordDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 事件转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Mapper(componentModel = "spring")
public interface ShipSailingRecordConvert {

    @Mapping(source = "id", target = "recordId")
    ShipSailingRecordDetailDTO convertToDetailDTO(ShipSailingRecordDO sailEventDO);

    List<ShipSailingRecordDetailDTO> convertToDetailDTOs(List<ShipSailingRecordDO> sailEventDOList);
}
