package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.ShipTimeConfigDO;
import com.deepinnet.sail.dal.mapper.ShipTimeConfigMapper;
import com.deepinnet.sail.service.repository.ShipTimeConfigRepository;
import org.springframework.stereotype.Repository;

/**
 * 船舶时间配置表 Repository 实现类
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Repository
public class ShipTimeConfigRepositoryImpl extends ServiceImpl<ShipTimeConfigMapper, ShipTimeConfigDO>
        implements ShipTimeConfigRepository {

    @Override
    public ShipTimeConfigDO findByTenantIdAndConfigType(String configType) {
        LambdaQueryWrapper<ShipTimeConfigDO> wrapper = Wrappers.lambdaQuery(ShipTimeConfigDO.class)
                .eq(ShipTimeConfigDO::getConfigType, configType)
                .eq(ShipTimeConfigDO::getIsEnabled, true);

        return baseMapper.selectOne(wrapper);
    }
}
