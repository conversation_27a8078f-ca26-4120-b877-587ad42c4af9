package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 点位详情查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "点位详情查询DTO", description = "查询点位详情的请求参数")
public class SailPointDetailQueryDTO {

    @ApiModelProperty(value = "点位ID", example = "1", required = true)
    @NotNull(message = "点位ID不能为空")
    private Long id;
}

