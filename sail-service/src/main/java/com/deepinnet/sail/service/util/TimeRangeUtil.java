package com.deepinnet.sail.service.util;

import com.deepinnet.sail.service.dto.SailingAnalysisQueryDTO;
import com.deepinnet.sail.service.enums.TimeRangeTypeEnum;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;

/**
 * 时间区间工具类
 *
 * <AUTHOR> wong
 * @since 2025-08-20
 */
public class TimeRangeUtil {
    /**
     * 判断时间跨度是否需要按小时统计（一天内）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true-按小时统计，false-按天统计
     */
    public static boolean shouldGroupByHour(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return false;
        }

        // 计算时间差，如果小于等于1天，则按小时统计
        long daysBetween = ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate());
        return daysBetween <= 1;
    }

    /**
     * 判断时间跨度类型
     *
     * @param timeRangeType 时间区间类型
     * @return true-按小时统计，false-按天统计
     */
    public static boolean shouldGroupByHour(TimeRangeTypeEnum timeRangeType) {
        return timeRangeType.isDayLevel();
    }
}
