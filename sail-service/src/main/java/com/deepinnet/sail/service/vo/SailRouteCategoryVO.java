package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 线路分类VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "线路分类VO", description = "线路分类信息")
public class SailRouteCategoryVO {

    @ApiModelProperty(value = "分类编码", example = "main_channel")
    private String code;

    @ApiModelProperty(value = "分类名称", example = "主航道")
    private String name;

    @ApiModelProperty(value = "该分类下的线路列表")
    private List<SailRouteListVO> routes;
}
