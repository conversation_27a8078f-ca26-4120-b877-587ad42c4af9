package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预警类型统计VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "预警类型统计VO", description = "预警类型统计信息，用于大屏展示")
public class WarningTypeStatisticsVO {

    @ApiModelProperty(value = "预警类型代码", example = "RISK_TIME_SAILING", notes = "可能值：RISK_TIME_SAILING(风险时段出海), SEA_AREA_DEVIATION(海域偏离), FORBIDDEN_AREA(前往禁海区), OVERDUE_RETURN(到时未归), DAILY_NOT_RETURN(当日未归), REMOTE_NOT_RETURN(异地未归)")
    private String warningType;

    @ApiModelProperty(value = "预警数量", example = "5")
    private Long count;
}