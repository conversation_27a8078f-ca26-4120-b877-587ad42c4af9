package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 船舶归港状态枚举
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Getter
@AllArgsConstructor
public enum SailingReturnStatusEnum {

    /**
     * 未归港
     */
    SAILING("SAILING", "未归"),

    /**
     * 已归港
     */
    RETURNED("RETURNED", "已归");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static SailingReturnStatusEnum getByCode(String code) {
        for (SailingReturnStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
