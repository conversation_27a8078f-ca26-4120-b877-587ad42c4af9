package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.SailPointDO;

import java.util.List;

/**
 * 点位表 Repository 接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface SailPointRepository extends IService<SailPointDO> {

    /**
     * 查询所有用户自定义点位列表，按创建时间倒序排序
     *
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsOrderByCreateTimeDesc();

    /**
     * 根据类型查询用户自定义点位列表，按创建时间倒序排序
     *
     * @param type 点位类型
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsByTypeOrderByCreateTimeDesc(String type);

    /**
     * 根据名称模糊查询用户自定义点位列表，按创建时间倒序排序
     *
     * @param name 点位名称
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsByNameLikeOrderByCreateTimeDesc(String name);

    /**
     * 根据类型和名称查询用户自定义点位列表，按创建时间倒序排序
     *
     * @param type 点位类型
     * @param name 点位名称
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsByTypeAndNameLikeOrderByCreateTimeDesc(String type, String name);

    /**
     * 检查点位名称是否已存在（排除指定ID）
     *
     * @param name 点位名称
     * @param excludeId 排除的点位ID，可为null
     * @return true如果存在，false如果不存在
     */
    boolean existsByNameAndIdNot(String name, Long excludeId);

    /**
     * 根据ID查询用户自定义点位
     *
     * @param id 点位ID
     * @return 点位信息
     */
    SailPointDO findUserDefinedPointById(Long id);

    /**
     * 根据租户ID查询所有用户自定义点位列表，按创建时间倒序排序
     *
     * @param tenantId 租户ID
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsByTenantIdOrderByCreateTimeDesc(String tenantId);

    /**
     * 根据租户ID和类型查询用户自定义点位列表，按创建时间倒序排序
     *
     * @param tenantId 租户ID
     * @param type 点位类型
     * @return 点位列表
     */
    List<SailPointDO> findUserDefinedPointsByTenantIdAndTypeOrderByCreateTimeDesc(String tenantId, String type);
}