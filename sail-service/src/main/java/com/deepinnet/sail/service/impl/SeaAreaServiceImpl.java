package com.deepinnet.sail.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.common.util.BizNoGenerateUtil;
import com.deepinnet.sail.dal.dataobject.SeaAreaDO;
import com.deepinnet.sail.service.SeaAreaService;
import com.deepinnet.sail.service.convert.SeaAreaConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.enums.AreaCategoryEnum;
import com.deepinnet.sail.service.repository.SeaAreaRepository;
import com.deepinnet.sail.service.vo.*;
import com.deepinnet.tenant.TenantIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 海域服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SeaAreaServiceImpl implements SeaAreaService {

    private final SeaAreaRepository seaAreaRepository;

    private final SeaAreaConvert seaAreaConvert;

    @Override
    public List<SeaAreaDTO> getSeaAreasByType(SeaAreaQueryDTO queryDTO) {
        // 根据类型查询海域数据
        List<SeaAreaDO> seaAreaDOList = seaAreaRepository.findByType(queryDTO.getType());

        // 转换为VO
        return seaAreaConvert.toVOList(seaAreaDOList);
    }

    @Override
    public List<SeaAreaCategoryVO> getAreaCategoryList(SeaAreaCategoryQueryDTO queryDTO) {
        // 查询所有用户自定义区域
        List<SeaAreaDO> allAreas = seaAreaRepository.findUserDefinedAreasOrderByCreateTimeDesc();

        // 转换为VO列表
        List<SeaAreaListVO> areaVOList = seaAreaConvert.toListVOList(allAreas);

        // 按类型分组
        Map<String, List<SeaAreaListVO>> typeMap = areaVOList.stream()
                .collect(Collectors.groupingBy(SeaAreaListVO::getType));

        // 构建类型列表
        List<SeaAreaCategoryVO> categoryList = new ArrayList<>();
        for (AreaCategoryEnum categoryEnum : AreaCategoryEnum.values()) {
            SeaAreaCategoryVO categoryVO = new SeaAreaCategoryVO();
            categoryVO.setCode(categoryEnum.getCode());
            categoryVO.setName(categoryEnum.getDesc());
            categoryVO.setAreas(typeMap.getOrDefault(categoryEnum.getCode(), new ArrayList<>()));
            categoryList.add(categoryVO);
        }

        return categoryList;
    }

    @Override
    public List<SeaAreaListVO> getAreaList(SeaAreaListQueryDTO queryDTO) {
        List<SeaAreaDO> seaAreaDOList;

        // 根据查询条件获取数据
        if (StringUtils.isNotBlank(queryDTO.getAreaCode())) {
            // 按区域编码查询（精确匹配）
            seaAreaDOList = seaAreaRepository.findUserDefinedAreasByAreaCodeOrderByCreateTimeDesc(queryDTO.getAreaCode());
        } else if (StringUtils.isNotBlank(queryDTO.getType()) && StringUtils.isNotBlank(queryDTO.getName())) {
            // 按类型和名称查询
            seaAreaDOList = seaAreaRepository.findUserDefinedAreasByCategoryAndNameLikeOrderByCreateTimeDesc(
                    queryDTO.getType(), queryDTO.getName());
        } else if (StringUtils.isNotBlank(queryDTO.getType())) {
            // 按类型查询
            seaAreaDOList = seaAreaRepository.findUserDefinedAreasByCategoryOrderByCreateTimeDesc(queryDTO.getType());
        } else if (StringUtils.isNotBlank(queryDTO.getName())) {
            // 按名称查询
            seaAreaDOList = seaAreaRepository.findUserDefinedAreasByNameLikeOrderByCreateTimeDesc(queryDTO.getName());
        } else {
            // 查询所有
            seaAreaDOList = seaAreaRepository.findUserDefinedAreasOrderByCreateTimeDesc();
        }

        return seaAreaConvert.toListVOList(seaAreaDOList);
    }

    @Override
    public SeaAreaDetailVO getAreaDetail(SeaAreaDetailQueryDTO queryDTO) {
        SeaAreaDO seaAreaDO = seaAreaRepository.findUserDefinedAreaById(queryDTO.getId());
        if (seaAreaDO == null) {
            throw new BizException(BizErrorCode.AREA_NOT_FOUND.getCode(), BizErrorCode.AREA_NOT_FOUND.getDesc());
        }

        return seaAreaConvert.toDetailVO(seaAreaDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createArea(SeaAreaCreateDTO createDTO) {
        // 验证区域类型
        if (!AreaCategoryEnum.isValidCode(createDTO.getType())) {
            throw new BizException(BizErrorCode.AREA_TYPE_INVALID.getCode(), BizErrorCode.AREA_TYPE_INVALID.getDesc());
        }

        // 检查区域名称是否已存在
        if (seaAreaRepository.existsByNameAndIdNot(createDTO.getName(), null)) {
            throw new BizException(BizErrorCode.AREA_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.AREA_NAME_ALREADY_EXISTS.getDesc());
        }

        // 检查区域编码是否已存在（如果提供了areaCode）
        if (StringUtils.isNotBlank(createDTO.getAreaCode()) && 
            seaAreaRepository.existsByAreaCodeAndIdNot(createDTO.getAreaCode(), null)) {
            throw new BizException(BizErrorCode.AREA_CODE_ALREADY_EXISTS.getCode(), BizErrorCode.AREA_CODE_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO对象
        SeaAreaDO seaAreaDO = seaAreaConvert.fromCreateDTO(createDTO);

        // 生成区域编码
        seaAreaDO.setCode(BizNoGenerateUtil.generateSeaAreaCode());

        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        seaAreaDO.setGmtCreated(now);
        seaAreaDO.setGmtModified(now);
        seaAreaDO.setTenantId(TenantIdUtil.getTenantId());

        // 保存到数据库
        seaAreaRepository.save(seaAreaDO);

        log.info("创建区域成功，区域ID：{}，区域名称：{}", seaAreaDO.getId(), seaAreaDO.getName());
        return seaAreaDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateArea(SeaAreaUpdateDTO updateDTO) {
        // 验证区域类型
        if (!AreaCategoryEnum.isValidCode(updateDTO.getType())) {
            throw new BizException(BizErrorCode.AREA_TYPE_INVALID.getCode(), BizErrorCode.AREA_TYPE_INVALID.getDesc());
        }

        // 检查区域是否存在
        SeaAreaDO existingArea = seaAreaRepository.findUserDefinedAreaById(updateDTO.getId());
        if (existingArea == null) {
            throw new BizException(BizErrorCode.AREA_NOT_FOUND.getCode(), BizErrorCode.AREA_NOT_FOUND.getDesc());
        }

        // 检查区域名称是否已存在（排除当前区域）
        if (seaAreaRepository.existsByNameAndIdNot(updateDTO.getName(), updateDTO.getId())) {
            throw new BizException(BizErrorCode.AREA_NAME_ALREADY_EXISTS.getCode(), BizErrorCode.AREA_NAME_ALREADY_EXISTS.getDesc());
        }

        // 检查区域编码是否已存在（如果提供了areaCode，排除当前区域）
        if (StringUtils.isNotBlank(updateDTO.getAreaCode()) && 
            seaAreaRepository.existsByAreaCodeAndIdNot(updateDTO.getAreaCode(), updateDTO.getId())) {
            throw new BizException(BizErrorCode.AREA_CODE_ALREADY_EXISTS.getCode(), BizErrorCode.AREA_CODE_ALREADY_EXISTS.getDesc());
        }

        // 转换为DO对象并设置需要更新的字段
        SeaAreaDO seaAreaDO = seaAreaConvert.fromUpdateDTO(updateDTO);
        seaAreaDO.setCode(existingArea.getCode());
        seaAreaDO.setTenantId(existingArea.getTenantId());
        seaAreaDO.setIsDeleted(existingArea.getIsDeleted());
        seaAreaDO.setSource(existingArea.getSource());
        seaAreaDO.setGmtCreated(existingArea.getGmtCreated());
        seaAreaDO.setGmtModified(LocalDateTime.now());

        // 更新数据库
        boolean success = seaAreaRepository.updateById(seaAreaDO);

        if (success) {
            log.info("更新区域成功，区域ID：{}，区域名称：{}", seaAreaDO.getId(), seaAreaDO.getName());
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteArea(Long id) {
        // 检查区域是否存在
        SeaAreaDO existingArea = seaAreaRepository.findUserDefinedAreaById(id);
        if (existingArea == null) {
            throw new BizException(BizErrorCode.AREA_NOT_FOUND.getCode(), BizErrorCode.AREA_NOT_FOUND.getDesc());
        }

        // 逻辑删除
        existingArea.setIsDeleted(true);
        existingArea.setGmtModified(LocalDateTime.now());

        boolean success = seaAreaRepository.updateById(existingArea);

        if (success) {
            log.info("删除区域成功，区域ID：{}，区域名称：{}", existingArea.getId(), existingArea.getName());
        }

        return success;
    }
}