package com.deepinnet.sail.service.dto;

import com.deepinnet.sail.service.enums.TimeRangeTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 船舶出海分析查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-20
 */
@Data
@ApiModel("船舶出海分析查询DTO")
public class SailingAnalysisQueryDTO {

    @ApiModelProperty(value = "时间区间类型：month-月份，day-天")
    @NotNull(message = "时间区间类型不能为空")
    private String timeRangeType;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 获取时间区间类型枚举
     *
     * @return 时间区间类型枚举
     */
    public TimeRangeTypeEnum getTimeRangeTypeEnum() {
        return TimeRangeTypeEnum.getByCode(this.timeRangeType);
    }
}
