package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wong
 * @create 2025/8/25 10:29
 * @Description
 *
 */
@Data
public class ShipSailingRecordDetailDTO {

    @ApiModelProperty(value = "船舶出海记录id")
    private Long recordId;

    /**
     * 船舶编号
     */
    @ApiModelProperty(value = "船舶编号")
    private String shipNo;

    /**
     * 船舶名称
     */
    @ApiModelProperty(value = "船舶名称")
    private String shipName;

    /**
     * 船舶关联的港口名称
     */
    @ApiModelProperty(value = "船舶关联的港口code")
    private String portCode;

    /**
     * 船舶关联的港口名称
     */
    @ApiModelProperty(value = "船舶关联的港口名称")
    private String portName;

    /**
     * 出港时间
     */
    @ApiModelProperty(value = "出港时间")
    private LocalDateTime departureTime;

    /**
     * 归港时间
     */
    @ApiModelProperty(value = "归港时间")
    private LocalDateTime returnTime;

    /**
     * 归港状态：SAILING-未归，RETURNED-已归
     */
    @ApiModelProperty(value = "归港状态：SAILING-未归，RETURNED-已归")
    private String returnStatus;
}
