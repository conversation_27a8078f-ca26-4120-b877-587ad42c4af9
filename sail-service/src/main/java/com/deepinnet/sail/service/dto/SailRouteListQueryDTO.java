package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 线路列表查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "线路列表查询DTO", description = "查询线路列表的请求参数")
public class SailRouteListQueryDTO {

    @ApiModelProperty(value = "线路类型", example = "main_channel", 
            notes = "可能值：main_channel(主航道), branch_channel(支航道), approach_channel(进港航道), departure_channel(出港航道), fishing_route(渔业航道), tourism_route(旅游航线), cargo_route(货运航线), emergency_route(应急航线)。为空时查询所有类型")
    private String type;

    @ApiModelProperty(value = "线路名称", example = "深圳港", notes = "支持模糊查询")
    private String name;
}
