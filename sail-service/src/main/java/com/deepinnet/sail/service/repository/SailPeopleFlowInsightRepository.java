package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO;

import java.util.List;

/**
 * 人流洞察数据仓储接口
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
public interface SailPeopleFlowInsightRepository extends IService<SailPeopleFlowInsightDO> {

    /**
     * 批量保存人流洞察数据
     *
     * @param dataList 数据列表
     * @return 是否保存成功
     */
    boolean batchSave(List<SailPeopleFlowInsightDO> dataList);
}