package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 人流活力指数查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Data
@ApiModel(value = "人流活力指数查询DTO", description = "查询人流活力指数的请求参数")
public class PeopleFlowVitalityIndexQueryDTO {

    @ApiModelProperty(value = "区域code", example = "SA001", required = true)
    @NotBlank(message = "区域code不能为空")
    private String areaCode;
}