package com.deepinnet.sail.service.enums;

import lombok.*;

/**
 * 区域分类枚举
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@AllArgsConstructor
public enum ProfileEnum {
    U_SEX("u_sex", "性别"),
    U_OCCUPATION("u_occupation", "职业"),
    U_AGE("u_age", "年龄"),
    U_PERMANENT_ADCODE("u_permanent_adcode", "常驻区域"),
    PROPERTY_LEVEL("property_level", "资产分布"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static ProfileEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ProfileEnum profileEnum : ProfileEnum.values()) {
            if (profileEnum.getCode().equals(code)) {
                return profileEnum;
            }
        }
        return null;
    }
}
