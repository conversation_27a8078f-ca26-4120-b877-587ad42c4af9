package com.deepinnet.sail.service.vo;

import com.deepinnet.sail.service.dto.PeopleFlowVitalityIndexDTO;
import io.swagger.annotations.*;
import lombok.Data;

import java.util.List;

/**
 * 人流活力指数VO
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Data
@ApiModel(value = "人流活力指数VO", description = "人流活力指数数据")
public class PeopleFlowVitalityIndexVO {

    @ApiModelProperty(value = "人流活力指数数据")
    private List<PeopleFlowVitalityIndexDTO> peopleFlowVitalityIndexDTOs;

    @ApiModelProperty(value = "总人数")
    private Integer totalFlow;
}