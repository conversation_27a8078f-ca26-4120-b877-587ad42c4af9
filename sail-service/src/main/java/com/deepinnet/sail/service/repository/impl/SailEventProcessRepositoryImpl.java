package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SailEventProcessDO;
import com.deepinnet.sail.dal.mapper.SailEventProcessMapper;
import com.deepinnet.sail.service.repository.SailEventProcessRepository;
import org.springframework.stereotype.Service;

/**
 * 事件处理记录Repository实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Service
public class SailEventProcessRepositoryImpl extends ServiceImpl<SailEventProcessMapper, SailEventProcessDO> implements SailEventProcessRepository {

}
