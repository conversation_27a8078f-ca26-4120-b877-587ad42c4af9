package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 今日船舶出海VO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel("今日船舶出海VO")
public class TodayShipSailingVO {

    @ApiModelProperty("今日出海船舶数（去重）")
    private Long todayShipCount;

    @ApiModelProperty("已归港船舶数")
    private Long returnedShipCount;

    @ApiModelProperty("未归港船舶数")
    private Long notReturnedShipCount;

    @ApiModelProperty("今日出海总次数")
    private Long todaySailingCount;

    @ApiModelProperty("自用船舶出海次数")
    private Long privateSailingCount;

    @ApiModelProperty("运营船舶出海次数")
    private Long operationalSailingCount;

    @ApiModelProperty("今日出海记录列表")
    private List<TodayShipSailingRecordVO> sailingRecords;
}
