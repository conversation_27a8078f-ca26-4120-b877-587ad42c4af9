package com.deepinnet.sail.service.vo;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 大屏事件数据VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "大屏事件数据VO", description = "大屏事件完整数据，包含统计信息和事件列表")
public class EventDashboardVO {

    @ApiModelProperty(value = "事件类型统计列表", notes = "用于饼图展示")
    private List<EventTypeStatisticsVO> typeStatistics;

    @ApiModelProperty(value = "事件分页列表", notes = "根据类型筛选的事件列表")
    private CommonPage<EventVO> eventList;
}