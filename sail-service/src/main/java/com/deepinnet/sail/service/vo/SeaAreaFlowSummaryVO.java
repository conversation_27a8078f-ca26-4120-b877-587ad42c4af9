package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.*;

/**
 * 海域人流统计VO
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Data
@ApiModel(value = "海域人流统计VO", description = "海域人流统计数据")
public class SeaAreaFlowSummaryVO {
    @ApiModelProperty(value = "海域code", example = "SEA001")
    private String seaAreaCode;

    @ApiModelProperty(value = "海域名称", example = "月亮湾海域")
    private String seaAreaName;

    @ApiModelProperty(value = "统计日期", example = "2025-08-29")
    private LocalDateTime summaryDate;

    @ApiModelProperty(value = "当日累计人流量", example = "12580")
    private String todayTotalFlow;
}