package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.condition.ShipWarningQueryCondition;
import com.deepinnet.sail.dal.dataobject.ShipWarningDO;
import com.deepinnet.sail.dal.dto.ShipWarningDTO;
import com.deepinnet.sail.dal.dto.WarningTypeStatisticsDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 船舶预警表 Repository 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
public interface ShipWarningRepository extends IService<ShipWarningDO> {

    /**
     * 根据出海记录ID和预警类型查询预警
     *
     * @param sailingRecordId 出海记录ID
     * @param warningType     预警类型
     * @return 预警信息
     */
    ShipWarningDO findByRecordIdAndType(Long sailingRecordId, String warningType);

    /**
     * 分页查询船舶预警记录（联表查询）
     * 直接联表查询ship_warning和ship表，返回完整的预警记录信息
     *
     * @param queryCondition 查询条件
     * @return 预警记录列表，包含船舶信息
     */
    List<ShipWarningDTO> selectShipWarningsWithShipInfo(ShipWarningQueryCondition queryCondition);

    /**
     * 统计各预警类型的数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预警类型统计列表
     */
    List<WarningTypeStatisticsDTO> getWarningTypeStatistics(LocalDateTime startTime, LocalDateTime endTime);
}
