package com.deepinnet.sail.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域分类枚举
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@AllArgsConstructor
public enum AreaCategoryEnum {

    /**
     * 禁海区
     */
    FORBIDDEN_AREA("forbidden_area", "禁海区"),

    /**
     * 普通海域
     */
    NORMAL_AREA("normal_area", "普通海域"),

    /**
     * 港口区域
     */
    PORT_AREA("port_area", "港口区域"),

    /**
     * 渔业区域
     */
    FISHING_AREA("fishing_area", "渔业区域"),

    /**
     * 锚泊区域
     */
    ANCHORAGE_AREA("anchorage_area", "锚泊区域"),

    /**
     * 航道区域
     */
    CHANNEL_AREA("channel_area", "航道区域"),

    /**
     * 保护区域
     */
    PROTECTED_AREA("protected_area", "保护区域"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static AreaCategoryEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (AreaCategoryEnum categoryEnum : AreaCategoryEnum.values()) {
            if (categoryEnum.getCode().equals(code)) {
                return categoryEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述，如果不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        AreaCategoryEnum categoryEnum = getByCode(code);
        return categoryEnum != null ? categoryEnum.getDesc() : "";
    }

    /**
     * 验证分类编码是否有效
     *
     * @param code 编码
     * @return true如果有效，false如果无效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
