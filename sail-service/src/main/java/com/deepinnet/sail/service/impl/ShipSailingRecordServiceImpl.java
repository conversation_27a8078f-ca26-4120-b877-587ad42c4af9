package com.deepinnet.sail.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;

import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.dal.dto.DailySailingCountDTO;
import com.deepinnet.sail.dal.dto.PortAreaSailingRankDTO;
import com.deepinnet.sail.service.convert.ShipSailingRecordConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.enums.*;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.sail.service.ShipSailingRecordService;
import com.deepinnet.sail.service.util.TimeRangeUtil;
import com.deepinnet.sail.service.vo.*;
import com.github.pagehelper.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船舶出海记录服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Service
public class ShipSailingRecordServiceImpl implements ShipSailingRecordService {

    @Resource
    private ShipSailingRecordRepository sailingRecordRepository;

    @Resource
    private ShipPositionRepository shipPositionRepository;

    @Resource
    private ShipWarningRepository shipWarningRepository;

    @Resource
    private ShipSailingRecordConvert shipSailingRecordConvert;

    @Override
    public CommonPage<SailingRecordVO> pageSailingRecords(SailingRecordQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        List<ShipSailingRecordDO> shipSailingRecordList;

        // 根据shipNo精确查询，给大屏用的
        if (StrUtil.isNotBlank(queryDTO.getShipNo())) {
            shipSailingRecordList = sailingRecordRepository.list(Wrappers.lambdaQuery(ShipSailingRecordDO.class)
                    .eq(ShipSailingRecordDO::getShipNo, queryDTO.getShipNo()));
        }

        // 如果有预警类型过滤，使用自定义查询
        else if (StrUtil.isNotBlank(queryDTO.getWarningType())) {
            // 使用自定义查询方法，支持预警类型过滤
            shipSailingRecordList = sailingRecordRepository.pageSailingRecordsWithWarning(queryDTO);
        } else {
            // 使用普通查询
            LambdaQueryWrapper<ShipSailingRecordDO> wrapper = buildQueryWrapper(queryDTO);
            shipSailingRecordList = sailingRecordRepository.list(wrapper);
        }

        if (CollUtil.isEmpty(shipSailingRecordList)) {
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO
        List<SailingRecordVO> voList = shipSailingRecordList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 批量查询预警信息
        if (CollUtil.isNotEmpty(voList)) {
            assembleRelatedShipWarningList(queryDTO, voList);
        }

        // 构建返回结果
        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), voList);
    }

    @Override
    public List<ShipSailingRecordTraceDTO> getShipSailingRecordTrace(ShipSailingRecordTraceQueryDTO queryDTO) {
        ShipSailingRecordDO sailingRecord = sailingRecordRepository.getById(queryDTO.getRecordId());
        if (sailingRecord == null) {
            throw new BizException(BizErrorCode.SAILING_RECORD_NOT_FOUND.getCode(), BizErrorCode.SAILING_RECORD_NOT_FOUND.getDesc());
        }

        // 查询船舶位置数据，出海记录离港时间-归港时间之内的所有位置数据
        LambdaQueryWrapper<ShipPositionDO> wrappers = Wrappers.lambdaQuery(ShipPositionDO.class)
                .eq(ShipPositionDO::getShipNo, queryDTO.getShipNo())
                .ge(ShipPositionDO::getReportTime, LocalDateTimeUtil.toEpochMilli(sailingRecord.getDepartureTime()));
        List<ShipPositionDO> positionList = shipPositionRepository.list();
        if (ObjectUtil.isNotEmpty(sailingRecord.getReturnTime())) {
            wrappers.le(ShipPositionDO::getReportTime, LocalDateTimeUtil.toEpochMilli(sailingRecord.getReturnTime()));
        }
        wrappers.orderByAsc(ShipPositionDO::getReportTime);

        if (CollUtil.isEmpty(positionList)) {
            return null;
        }

        return convertToTraceDTOs(positionList);
    }

    @Override
    public CommonPage<ShipSailingRecordDetailDTO> pageShipSailingRecordDetails(ShipSailingRecordDetailQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        List<ShipSailingRecordDO> shipSailingDOs = sailingRecordRepository.list(Wrappers.lambdaQuery(ShipSailingRecordDO.class)
                .eq(ShipSailingRecordDO::getShipNo, queryDTO.getShipNo()));

        if (CollUtil.isEmpty(shipSailingDOs)) {
            return CommonPage.buildEmptyPage();
        }

        List<ShipSailingRecordDetailDTO> shipSailingRecordDetailDTOs = shipSailingRecordConvert.convertToDetailDTOs(shipSailingDOs);
        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), shipSailingRecordDetailDTOs);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ShipSailingRecordDO> buildQueryWrapper(SailingRecordQueryDTO queryDTO) {
        LambdaQueryWrapper<ShipSailingRecordDO> wrapper = Wrappers.lambdaQuery(ShipSailingRecordDO.class);

        // 船舶名称模糊查询
        if (StrUtil.isNotBlank(queryDTO.getShipName())) {
            wrapper.like(ShipSailingRecordDO::getShipName, queryDTO.getShipName());
        }

        // 所属片区
        if (StrUtil.isNotBlank(queryDTO.getPortName())) {
            wrapper.eq(ShipSailingRecordDO::getPortCode, queryDTO.getPortName());
        }

        // 出港日期范围
        if (queryDTO.getDepartureStartDate() != null) {
            LocalDateTime startDateTime = queryDTO.getDepartureStartDate();
            wrapper.ge(ShipSailingRecordDO::getDepartureTime, startDateTime);
        }

        if (queryDTO.getDepartureEndDate() != null) {
            LocalDateTime endDateTime = queryDTO.getDepartureEndDate();
            wrapper.le(ShipSailingRecordDO::getDepartureTime, endDateTime);
        }

        // 归港状态
        if (StrUtil.isNotBlank(queryDTO.getReturnStatus())) {
            wrapper.eq(ShipSailingRecordDO::getReturnStatus, queryDTO.getReturnStatus());
        }

        // 按出港时间倒序排列
        wrapper.orderByDesc(ShipSailingRecordDO::getDepartureTime);

        return wrapper;
    }

    private void assembleRelatedShipWarningList(SailingRecordQueryDTO queryDTO, List<SailingRecordVO> voList) {
        List<Long> recordIds = voList.stream()
                .map(SailingRecordVO::getId)
                .collect(Collectors.toList());

        Map<Long, List<ShipWarningDO>> warningMap = getWarningsByRecordIds(recordIds, queryDTO.getWarningType());

        // 填充预警信息
        voList.forEach(vo -> {
            List<ShipWarningDO> warnings = warningMap.get(vo.getId());
            if (CollUtil.isNotEmpty(warnings)) {
                List<ShipWarningInfoVO> warningVOs = warnings.stream()
                        .map(this::convertToWarningVO)
                        .collect(Collectors.toList());
                vo.setWarnings(warningVOs);
            }
        });
    }

    /**
     * 批量查询预警信息
     */
    private Map<Long, List<ShipWarningDO>> getWarningsByRecordIds(List<Long> recordIds, String warningType) {
        LambdaQueryWrapper<ShipWarningDO> wrapper = Wrappers.lambdaQuery(ShipWarningDO.class)
                .in(ShipWarningDO::getSailingRecordId, recordIds);

        // 如果指定了预警类型，则过滤
        if (StrUtil.isNotBlank(warningType)) {
            wrapper.eq(ShipWarningDO::getWarningType, warningType);
        }

        wrapper.orderByAsc(ShipWarningDO::getTriggerTime);

        List<ShipWarningDO> warnings = shipWarningRepository.list(wrapper);

        return warnings.stream()
                .collect(Collectors.groupingBy(ShipWarningDO::getSailingRecordId));
    }

    /**
     * 转换为VO
     */
    private SailingRecordVO convertToVO(ShipSailingRecordDO record) {
        SailingRecordVO vo = new SailingRecordVO();
        vo.setId(record.getId());
        vo.setShipNo(record.getShipNo());
        vo.setShipName(record.getShipName());
        vo.setPortCode(record.getPortCode());
        vo.setPortName(record.getPortName());
        vo.setDepartureTime(record.getDepartureTime());
        vo.setReturnTime(record.getReturnTime());
        vo.setStatus(record.getReturnStatus());
        return vo;
    }

    /**
     * 转换预警信息为VO
     */
    private ShipWarningInfoVO convertToWarningVO(ShipWarningDO warning) {
        ShipWarningInfoVO vo = new ShipWarningInfoVO();
        vo.setId(warning.getId());
        vo.setWarningType(warning.getWarningType());
        vo.setDescription(warning.getDescription());
        vo.setTriggerTime(warning.getTriggerTime());
        return vo;
    }

    private List<ShipSailingRecordTraceDTO> convertToTraceDTOs(List<ShipPositionDO> positionList) {
        if (CollUtil.isEmpty(positionList)) {
            return null;
        }

        List<ShipSailingRecordTraceDTO> shipSailingRecordTraceDTOs = new ArrayList<>();
        positionList.forEach(position -> {
            ShipSailingRecordTraceDTO recordTraceDTO = new ShipSailingRecordTraceDTO();
            recordTraceDTO.setX(position.getX());
            recordTraceDTO.setY(position.getY());
            shipSailingRecordTraceDTOs.add(recordTraceDTO);
        });

        return shipSailingRecordTraceDTOs;
    }

    @Override
    public SailingAnalysisVO getSailingAnalysis(SailingAnalysisQueryDTO queryDTO) {
        LocalDateTime startTime = queryDTO.getStartTime();
        LocalDateTime endTime = queryDTO.getEndTime();
        TimeRangeTypeEnum timeRangeType = queryDTO.getTimeRangeTypeEnum();

        SailingAnalysisVO analysisVO = new SailingAnalysisVO();

        // 1. 统计出海总次数
        Long totalSailingCount = sailingRecordRepository.countSailingRecords(startTime, endTime);
        analysisVO.setTotalSailingCount(totalSailingCount != null ? totalSailingCount : 0L);

        // 2. 统计出海船舶数（去重）
        Long uniqueShipCount = sailingRecordRepository.countUniqueShips(startTime, endTime);
        analysisVO.setUniqueShipCount(uniqueShipCount != null ? uniqueShipCount : 0L);

        // 3. 根据时间跨度选择统计粒度
        List<DailySailingCountDTO> timeCounts;
        if (TimeRangeUtil.shouldGroupByHour(timeRangeType)) {
            // 日级别：按小时统计
            timeCounts = sailingRecordRepository.getHourlySailingCounts(startTime, endTime);
        } else {
            // 月级别或自定义：按天统计
            timeCounts = sailingRecordRepository.getDailySailingCounts(startTime, endTime);
        }

        List<DailySailingCountVO> dailySailingCounts = generateDailySailingCount(timeCounts);
        analysisVO.setDailySailingCounts(dailySailingCounts);
        return analysisVO;
    }

    @Override
    public List<PortAreaSailingRankVO> getPortSailingRank(SailingAnalysisQueryDTO queryDTO) {
        LocalDateTime startTime = queryDTO.getStartTime();
        LocalDateTime endTime = queryDTO.getEndTime();

        // 港口片区出海次数排行
        List<PortAreaSailingRankDTO> portAreaRanking = sailingRecordRepository.getPortAreaSailingRanking(startTime, endTime);
        return generatePortAreaRank(portAreaRanking);
    }


    private List<DailySailingCountVO> generateDailySailingCount(List<DailySailingCountDTO> timeCounts) {
        return timeCounts.stream()
                .map(dto -> {
                    DailySailingCountVO dailyVO = new DailySailingCountVO();
                    dailyVO.setDate(dto.getDate());
                    dailyVO.setCount(dto.getCount());
                    return dailyVO;
                })
                .collect(Collectors.toList());
    }

    private List<PortAreaSailingRankVO> generatePortAreaRank(List<PortAreaSailingRankDTO> portAreaRanking) {
        List<PortAreaSailingRankVO> portAreaRankingList = new ArrayList<>();
        for (int i = 0; i < portAreaRanking.size(); i++) {
            PortAreaSailingRankDTO dto = portAreaRanking.get(i);
            PortAreaSailingRankVO rankVO = new PortAreaSailingRankVO();
            rankVO.setRank(i + 1);
            rankVO.setPortName(dto.getPortArea());
            rankVO.setCount(dto.getCount());
            portAreaRankingList.add(rankVO);
        }
        return portAreaRankingList;
    }

    @Override
    public TodayShipSailingVO getTodayShipSailing(TodayShipSailingQueryDTO queryDTO) {
        // 计算今日的开始和结束时间
        LocalDateTime startTime = queryDTO.getStartTime();
        LocalDateTime endTime = queryDTO.getEndTime();

        String portCode = queryDTO.getPortCode();
        // 如果传入空值，则设置为null查询所有片区
        if (StrUtil.isBlank(portCode)) {
            portCode = null;
        }

        TodayShipSailingVO result = new TodayShipSailingVO();

        // 构建基础查询条件
        LambdaQueryWrapper<ShipSailingRecordDO> baseWrapper = Wrappers.lambdaQuery(ShipSailingRecordDO.class)
                .between(ShipSailingRecordDO::getDepartureTime, startTime, endTime);

        if (StrUtil.isNotBlank(portCode)) {
            baseWrapper.eq(ShipSailingRecordDO::getPortCode, portCode);
        }

        // 统计今日出海船舶数（去重）
        Long uniqueShipCount = sailingRecordRepository.countDistinctTodayShips(startTime, endTime, portCode);
        result.setTodayShipCount(uniqueShipCount != null ? uniqueShipCount : 0L);

        // 统计已归港船舶数（去重）
        Long returnedShipCount = sailingRecordRepository.countDistinctReturnedShips(startTime, endTime, portCode);
        result.setReturnedShipCount(returnedShipCount != null ? returnedShipCount : 0L);

        // 统计未归港船舶数（去重）
        Long notReturnedShipCount = result.getTodayShipCount() - result.getReturnedShipCount();
        result.setNotReturnedShipCount(notReturnedShipCount);

        // 统计今日出海总次数
        Long todaySailingCount = sailingRecordRepository.count(baseWrapper.clone());
        result.setTodaySailingCount(todaySailingCount);

        // 统计自用船舶出海次数（联表查询）
        Long privateSailingCount = sailingRecordRepository.countTodayShipSailingByType(startTime, endTime, portCode, ShipTypeEnum.PRIVATE.getCode());
        result.setPrivateSailingCount(privateSailingCount != null ? privateSailingCount : 0L);

        // 统计运营船舶出海次数（联表查询）
        Long operationalSailingCount = sailingRecordRepository.countTodayShipSailingByType(startTime, endTime, portCode, ShipTypeEnum.OPERATIONAL.getCode());
        result.setOperationalSailingCount(operationalSailingCount != null ? operationalSailingCount : 0L);

        // 查询今日出海记录列表
        // SAILING 会排在 RETURNED 前面
        List<ShipSailingRecordDO> sailingRecords = sailingRecordRepository.list(
                baseWrapper.clone()
                        .orderByAsc(ShipSailingRecordDO::getReturnStatus)
                        .orderByDesc(ShipSailingRecordDO::getDepartureTime)
        );

        if (CollUtil.isEmpty(sailingRecords)) {
            return result;
        }

        List<Long> sailingRecordIds = sailingRecords.stream().map(ShipSailingRecordDO::getId)
                .collect(Collectors.toList());
        List<ShipWarningDO> shipWarningList = shipWarningRepository.list(Wrappers.lambdaQuery(ShipWarningDO.class)
                .in(ShipWarningDO::getSailingRecordId, sailingRecordIds));
        Map<String, List<ShipWarningDO>> shipWarningMap = new HashMap<>();
        if (CollUtil.isNotEmpty(shipWarningList)) {
            shipWarningMap = shipWarningList.stream()
                    .collect(Collectors.groupingBy(ShipWarningDO::getShipNo));
        }

        Map<String, List<ShipWarningDO>> finalShipWarningMap = shipWarningMap;
        List<TodayShipSailingRecordVO> sailingRecordVOs = sailingRecords.stream()
                .map(record -> {
                    return buildTodayRecordVO(record, finalShipWarningMap);
                })
                .collect(Collectors.toList());
        result.setSailingRecords(sailingRecordVOs);

        return result;
    }

    private TodayShipSailingRecordVO buildTodayRecordVO(ShipSailingRecordDO record, Map<String, List<ShipWarningDO>> finalShipWarningMap) {
        TodayShipSailingRecordVO recordVO = new TodayShipSailingRecordVO();
        recordVO.setShipName(record.getShipName());
        recordVO.setShipNo(record.getShipNo());
        recordVO.setDepartureTime(record.getDepartureTime());
        recordVO.setReturnStatus(record.getReturnStatus());
        if (MapUtil.isNotEmpty(finalShipWarningMap) && CollUtil.isNotEmpty(finalShipWarningMap.get(record.getShipNo()))) {
            recordVO.setShipWarningList(finalShipWarningMap.get(record.getShipNo()).stream()
                    .map(ShipWarningDO::getWarningType)
                    .collect(Collectors.toList()));
        }
        return recordVO;
    }
}
