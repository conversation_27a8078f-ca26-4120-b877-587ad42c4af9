package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 区域分类VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "区域分类VO", description = "区域分类信息")
public class SeaAreaCategoryVO {

    @ApiModelProperty(value = "分类编码", example = "forbidden_area")
    private String code;

    @ApiModelProperty(value = "分类名称", example = "禁海区")
    private String name;

    @ApiModelProperty(value = "该分类下的区域列表")
    private List<SeaAreaListVO> areas;
}
