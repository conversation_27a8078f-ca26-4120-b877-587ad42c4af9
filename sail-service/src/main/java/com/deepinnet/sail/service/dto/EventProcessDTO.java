package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 事件处理DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件处理DTO", description = "事件处理参数")
public class EventProcessDTO {

    @ApiModelProperty(value = "事件编号")
    @NotBlank(message = "事件编号不能为空")
    private String eventNo;

    @ApiModelProperty(value = "处理阶段", example = "process", notes = "必填，可选值：process-处理，complete-办结，close-关闭")
    @NotBlank(message = "处理阶段不能为空")
    private String processStage;

    @ApiModelProperty(value = "处理意见", example = "已派遣救援队前往现场", notes = "选填，最大长度500字符")
    @Size(max = 500, message = "处理意见最大长度为500字符")
    private String processOpinion;
}
