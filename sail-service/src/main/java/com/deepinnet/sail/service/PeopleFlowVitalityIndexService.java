package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.PeopleFlowVitalityIndexQueryDTO;
import com.deepinnet.sail.service.vo.PeopleFlowVitalityIndexVO;

/**
 * 人流活力指数服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
public interface PeopleFlowVitalityIndexService {

    /**
     * 根据区域code查询最新批次的人流活力指数数据
     *
     * @param queryDTO 查询条件
     * @return 最新批次的人流活力指数数据列表
     */
    PeopleFlowVitalityIndexVO getLatestByAreaCode(PeopleFlowVitalityIndexQueryDTO queryDTO);
}