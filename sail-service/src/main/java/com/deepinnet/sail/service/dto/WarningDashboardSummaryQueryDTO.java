package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 大屏预警总览请求DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "大屏预警总览请求DTO", description = "获取大屏预警总览数据的请求参数")
public class WarningDashboardSummaryQueryDTO {

    @ApiModelProperty(value = "开始时间", example = "2025-08-01 00:00:00", notes = "预警触发时间范围查询的开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-08-31 23:59:59", notes = "预警触发时间范围查询的结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
}