package com.deepinnet.sail.service.vo;

import io.swagger.annotations.*;
import lombok.Data;

/**
 * 船舶详细信息VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "船舶详细信息VO", description = "船舶详细信息，包含位置坐标")
public class ShipDetailVO {

    @ApiModelProperty(value = "船舶编号", example = "CB9837216")
    private String shipNo;

    @ApiModelProperty(value = "船舶名称", example = "南澳旅游096")
    private String shipName;

    @ApiModelProperty(value = "纳管公司", example = "南澳股份合作公司")
    private String managementCompany;

    @ApiModelProperty(value = "所有人", example = "张德武")
    private String owner;

    @ApiModelProperty(value = "是否纳管", example = "true")
    private Boolean isManaged;

    @ApiModelProperty(value = "船舶类型", example = "operation_management", 
            notes = "可能值：operation_management(运管), self_use(自用)")
    private String shipType;

    @ApiModelProperty(value = "船主联系电话", example = "13642342344")
    private String ownerPhone;

    @ApiModelProperty(value = "自用船舶是否签署承诺书", example = "true")
    private Boolean isCommitmentSigned;

    @ApiModelProperty(value = "是否备案", example = "true")
    private Boolean isFiled;

    @ApiModelProperty(value = "包干责任人", example = "张德武")
    private String responsiblePerson;

    @ApiModelProperty(value = "责任人联系电话", example = "13642342344")
    private String responsiblePersonPhone;

    @ApiModelProperty(value = "港口编号", example = "PORT001")
    private String portCode;

    @ApiModelProperty(value = "所属港口", example = "深圳港")
    private String portName;

    @ApiModelProperty(value = "所属办事处", example = "南澳办事处")
    private String office;

    @ApiModelProperty(value = "备注")
    private String remarks;
}