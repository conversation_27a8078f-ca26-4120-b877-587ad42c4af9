package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SeaAreaFlowSummaryDO;
import com.deepinnet.sail.dal.mapper.SeaAreaFlowSummaryMapper;
import com.deepinnet.sail.service.repository.SeaAreaFlowSummaryRepository;
import org.springframework.stereotype.Repository;

/**
 * 海域每日人流统计Repository实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Repository
public class SeaAreaFlowSummaryRepositoryImpl extends ServiceImpl<SeaAreaFlowSummaryMapper, SeaAreaFlowSummaryDO>
        implements SeaAreaFlowSummaryRepository {

}