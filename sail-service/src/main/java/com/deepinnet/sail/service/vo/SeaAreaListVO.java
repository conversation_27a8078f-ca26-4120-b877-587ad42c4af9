package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 区域列表VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "区域列表VO", description = "区域列表信息")
public class SeaAreaListVO {

    @ApiModelProperty(value = "区域ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "区域代码", example = "SA001")
    private String code;

    @ApiModelProperty(value = "区域编码", example = "AREA001")
    private String areaCode;

    @ApiModelProperty(value = "区域名称", example = "深圳港禁海区1")
    private String name;

    @ApiModelProperty(value = "区域类型", example = "forbidden_area")
    private String type;

    @ApiModelProperty(value = "区域类型名称", example = "禁海区")
    private String typeName;

    @ApiModelProperty(value = "区域面积", example = "100.5")
    private String area;

    @ApiModelProperty(value = "区域来源", example = "userDefined", 
            notes = "可能值：userDefined(自建海域), gaoDe(高德同步的海域)")
    private String source;

    @ApiModelProperty(value = "创建时间", example = "2025-01-15T10:30:00")
    private LocalDateTime gmtCreated;
}