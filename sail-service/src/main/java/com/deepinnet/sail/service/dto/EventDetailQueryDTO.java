package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 事件详情查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件详情查询DTO", description = "查询事件详情参数")
public class EventDetailQueryDTO {

    @ApiModelProperty(value = "事件编号", example = "1", notes = "必填")
    @NotNull(message = "事件编号不能为空")
    private String eventNo;
}
