package com.deepinnet.sail.service.vo;

import io.swagger.annotations.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件处理记录VO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件处理记录VO", description = "事件处理记录信息")
public class EventProcessVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID")
    private String eventNo;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private String processorNo;

    /**
     * 处理人姓名
     */
    @ApiModelProperty(value = "处理人姓名")
    private String processorName;

    /**
     * 处理部门
     */
    @ApiModelProperty(value = "处理部门编号")
    private Long processDepartment;

    /**
     * 处理部门
     */
    @ApiModelProperty(value = "处理部门编号")
    private List<String> processFullDepartmentName;

    /**
     * 处理阶段
     */
    @ApiModelProperty(value = "处理阶段：process-处理，complete-办结，close-关闭")
    private String processStage;

    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见")
    private String processOpinion;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private LocalDateTime processTime;
}
