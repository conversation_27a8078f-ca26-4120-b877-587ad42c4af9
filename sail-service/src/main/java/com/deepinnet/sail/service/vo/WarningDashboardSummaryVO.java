package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 大屏预警总览VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "大屏预警总览VO", description = "大屏预警总览数据")
public class WarningDashboardSummaryVO {

    @ApiModelProperty(value = "预警类型统计列表", notes = "各预警类型的数量统计")
    private List<WarningTypeStatisticsVO> warningStatistics;

    @ApiModelProperty(value = "预警总数", example = "12")
    private Long totalCount;
}