package com.deepinnet.sail.service.enums;

import lombok.*;

/**
 * 海域类型枚举
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum ShipWarningTypeEnum {

    RISK_TIME_SAILING("risk_time_sailing", "风险时段出海"),

    SEA_AREA_DEVIATION("sea_area_deviation", "海域偏离"),

    FORBIDDEN_AREA("forbidden_area", "前往禁海区"),

    OVERDUE_RETURN("overdue_return", "到时未归"),

    DAILY_NOT_RETURN("daily_not_return", "当日未归"),

    REMOTE_NOT_RETURN("remote_not_return", "异地未归"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static ShipWarningTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ShipWarningTypeEnum typeEnum : ShipWarningTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
