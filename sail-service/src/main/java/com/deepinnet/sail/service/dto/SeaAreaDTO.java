package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 海域信息VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "海域信息VO", description = "海域/港口信息")
public class SeaAreaDTO {

    @ApiModelProperty(value = "海域ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "海域代码", example = "SA001")
    private String code;

    @ApiModelProperty(value = "海域名称", example = "深圳港")
    private String name;

    @ApiModelProperty(value = "海域类型", example = "port", 
            notes = "可能值：forbidden(禁海区), normal(普通海域), port(港口)")
    private String type;

    @ApiModelProperty(value = "海域来源", example = "userDefined", 
            notes = "可能值：userDefined(自建海域), gaoDe(高德同步的海域)")
    private String source;

    @ApiModelProperty(value = "面积", example = "100.5")
    private String area;

    @ApiModelProperty(value = "海域的WKT几何信息")
    private String wkt;
}