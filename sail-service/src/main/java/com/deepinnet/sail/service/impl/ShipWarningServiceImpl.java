package com.deepinnet.sail.service.impl;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.dal.condition.ShipWarningQueryCondition;
import com.deepinnet.sail.dal.dto.*;
import com.deepinnet.sail.service.ShipWarningService;
import com.deepinnet.sail.service.convert.ShipWarningConvert;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.repository.ShipWarningRepository;
import com.deepinnet.sail.service.vo.*;
import com.github.pagehelper.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 船舶预警记录服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-22
 */
@Service
@RequiredArgsConstructor
public class ShipWarningServiceImpl implements ShipWarningService {

    private final ShipWarningRepository shipWarningRepository;

    private final ShipWarningConvert shipWarningConvert;

    @Override
    public CommonPage<ShipWarningVO> pageShipWarnings(ShipWarningQueryDTO queryDTO) {
        // 使用联表查询
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        ShipWarningQueryCondition queryCondition = shipWarningConvert.toCondition(queryDTO);

        Page<ShipWarningDTO> page = (Page<ShipWarningDTO>) shipWarningRepository.selectShipWarningsWithShipInfo(queryCondition);

        List<ShipWarningVO> shipWarningVO = page.getResult().stream()
                .map(shipWarningConvert::toVO)
                .collect(Collectors.toList());

        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(),
                page.getPages(), page.getTotal(), shipWarningVO);
    }

    @Override
    public WarningDashboardSummaryVO getWarningDashboardSummary(WarningDashboardSummaryQueryDTO queryDTO) {
        // 获取预警类型统计数据
        List<WarningTypeStatisticsDTO> statisticsDTOList = shipWarningRepository.getWarningTypeStatistics(
                queryDTO.getStartTime(), queryDTO.getEndTime());

        // 转换为VO
        List<WarningTypeStatisticsVO> statisticsVOList = statisticsDTOList.stream()
                .map(dto -> {
                    WarningTypeStatisticsVO vo = new WarningTypeStatisticsVO();
                    vo.setWarningType(dto.getWarningType());
                    vo.setCount(dto.getCount());
                    return vo;
                })
                .collect(Collectors.toList());

        // 计算总数
        Long totalCount = statisticsVOList.stream()
                .mapToLong(WarningTypeStatisticsVO::getCount)
                .sum();

        // 构建返回结果
        WarningDashboardSummaryVO result = new WarningDashboardSummaryVO();
        result.setWarningStatistics(statisticsVOList);
        result.setTotalCount(totalCount);

        return result;
    }
}
