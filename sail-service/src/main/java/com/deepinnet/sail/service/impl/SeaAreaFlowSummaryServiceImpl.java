package com.deepinnet.sail.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.dal.dataobject.SeaAreaFlowSummaryDO;
import com.deepinnet.sail.service.SeaAreaFlowSummaryService;
import com.deepinnet.sail.service.convert.SeaAreaFlowStatisticsConvert;
import com.deepinnet.sail.service.dto.SeaAreaFlowSummaryQueryDTO;
import com.deepinnet.sail.service.repository.SeaAreaFlowSummaryRepository;
import com.deepinnet.sail.service.vo.SeaAreaFlowSummaryVO;
import com.github.pagehelper.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 海域人流统计服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Service
@RequiredArgsConstructor
public class SeaAreaFlowSummaryServiceImpl implements SeaAreaFlowSummaryService {

    private final SeaAreaFlowSummaryRepository seaAreaFlowSummaryRepository;

    private final SeaAreaFlowStatisticsConvert seaAreaFlowStatisticsConvert;

    @Override
    public CommonPage<SeaAreaFlowSummaryVO> pageQuery(SeaAreaFlowSummaryQueryDTO queryDTO) {
        // 开启分页
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<SeaAreaFlowSummaryDO> wrapper = Wrappers.lambdaQuery(SeaAreaFlowSummaryDO.class)
                .eq(SeaAreaFlowSummaryDO::getSeaAreaCode, queryDTO.getAreaCode())
                .ge(SeaAreaFlowSummaryDO::getSummaryDate, queryDTO.getStartDate())
                .le(SeaAreaFlowSummaryDO::getSummaryDate, queryDTO.getEndDate())
                .orderByDesc(SeaAreaFlowSummaryDO::getTodayTotalFlow);

        // 查询数据
        List<SeaAreaFlowSummaryDO> dataList = seaAreaFlowSummaryRepository.list(wrapper);

        if (CollUtil.isEmpty(dataList)) {
            LogUtil.info("未查询到海域人流统计数据");
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO
        List<SeaAreaFlowSummaryVO> voList = seaAreaFlowStatisticsConvert.toVOList(dataList);

        // 构建分页信息
        PageInfo<SeaAreaFlowSummaryVO> pageInfo = PageInfo.of(voList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(),
                pageInfo.getPages(), pageInfo.getTotal(), voList);

    }
}