package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 事件处理记录查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件处理记录查询DTO", description = "根据事件编号查询处理记录")
public class EventProcessRecordQueryDTO {

    @ApiModelProperty(value = "事件编号", example = "2025082100001", notes = "必填")
    @NotBlank(message = "事件编号不能为空")
    private String eventNo;
}
