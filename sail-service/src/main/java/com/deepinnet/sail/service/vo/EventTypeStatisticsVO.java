package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事件类型统计VO
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Data
@ApiModel(value = "事件类型统计VO", description = "事件类型统计信息，用于大屏饼图展示")
public class EventTypeStatisticsVO {

    @ApiModelProperty(value = "事件类型代码", example = "tourist_rescue")
    private String eventSource;

    @ApiModelProperty(value = "事件数量", example = "15")
    private Long count;

    @ApiModelProperty(value = "占比", example = "35.5", notes = "百分比，保留一位小数")
    private Double percentage;
}