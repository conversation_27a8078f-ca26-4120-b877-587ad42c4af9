package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 区域详情查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "区域详情查询DTO", description = "查询区域详情的请求参数")
public class SeaAreaDetailQueryDTO {

    @ApiModelProperty(value = "区域ID", example = "1", required = true)
    @NotNull(message = "区域ID不能为空")
    private Long id;
}
