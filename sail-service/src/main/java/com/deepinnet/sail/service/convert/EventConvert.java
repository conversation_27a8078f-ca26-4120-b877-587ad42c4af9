package com.deepinnet.sail.service.convert;

import cn.hutool.json.JSONUtil;
import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.service.vo.*;
import org.mapstruct.*;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 事件转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Mapper(componentModel = "spring")
public interface EventConvert {

    /**
     * EventDO转EventVO
     */
    @Mapping(target = "attachmentUrls", source = "attachments", qualifiedByName = "attachmentsToUrls")
    EventVO toVO(SailEventDO sailEventDO);

    /**
     * EventProcessDO转EventProcessVO
     */
    EventProcessVO toProcessVO(SailEventProcessDO sailEventProcessDO);

    /**
     * 附件JSON字符串转URL列表
     */
    @Named("attachmentsToUrls")
    default List<String> attachmentsToUrls(String attachments) {
        if (!StringUtils.hasText(attachments)) {
            return null;
        }

        return JSONUtil.toList(attachments, String.class);
    }
}
