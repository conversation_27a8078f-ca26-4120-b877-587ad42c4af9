package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.PeopleFlowVitalityIndexDO;

import java.util.List;

/**
 * 人流活力指数Repository接口
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
public interface PeopleFlowVitalityIndexRepository extends IService<PeopleFlowVitalityIndexDO> {

    /**
     * 根据区域code查询最新批次的人流活力指数数据
     *
     * @param areaCode 区域code
     * @return 最新批次的人流活力指数数据列表
     */
    List<PeopleFlowVitalityIndexDO> findLatestByAreaCode(String areaCode);
}