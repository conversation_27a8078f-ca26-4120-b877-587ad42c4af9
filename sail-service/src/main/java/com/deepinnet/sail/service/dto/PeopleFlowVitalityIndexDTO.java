package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人流活力指数VO
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Data
@ApiModel(value = "人流活力指数VO", description = "人流活力指数数据")
public class PeopleFlowVitalityIndexDTO {

    @ApiModelProperty(value = "区域code", example = "SA001")
    private String areaCode;

    @ApiModelProperty(value = "海域code", example = "SEA001")
    private String seaAreaCode;

    @ApiModelProperty(value = "时间戳", example = "1693276800000")
    private Long time;

    @ApiModelProperty(value = "批次号", example = "1693276800000")
    private Long batchId;

    @ApiModelProperty(value = "人流活力指数", example = "0.85")
    private String activityIndex;

    @ApiModelProperty(value = "人流量(当前批次)", example = "1200")
    private String flow;

    @ApiModelProperty(value = "上一个批次人流量", example = "1150")
    private String preFlow;

    @ApiModelProperty(value = "今日累计人流量", example = "15600")
    private String todayCumulativeFlow;

    @ApiModelProperty(value = "历史平均人流量", example = "1180")
    private String avgFlow;

    @ApiModelProperty(value = "历史最大人流量", example = "2500")
    private String maxFlow;
}