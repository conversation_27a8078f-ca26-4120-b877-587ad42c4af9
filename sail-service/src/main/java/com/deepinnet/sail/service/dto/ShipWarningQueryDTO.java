package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 船舶预警记录查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-22
 */
@Data
@ApiModel(value = "船舶预警记录查询DTO", description = "船舶预警记录分页查询参数")
public class ShipWarningQueryDTO {

    @ApiModelProperty(value = "船舶名称", example = "深海渔船135")
    private String shipName;

    @ApiModelProperty(value = "所属片区", example = "西涌片区")
    private String portCode;

    @ApiModelProperty(value = "船舶所有人", example = "张三")
    private String owner;

    @ApiModelProperty(value = "船主联系电话", example = "13800138000")
    private String ownerPhone;

    @ApiModelProperty(value = "预警类型", notes = "可选值：risk_time_sailing(风险时段出海)、sea_area_deviation(海域偏离)、forbidden_area(前往禁海区)、overdue_return(到时未归)、daily_not_return(当日未归)、remote_not_return(异地未归)")
    private String warningType;

    @ApiModelProperty(value = "预警时间开始", example = "2025-07-01 00:00:00")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "预警时间结束", example = "2025-07-31 23:59:59")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "页码", example = "1", notes = "必填，从1开始")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小", example = "10", notes = "必填，最大100")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize;
}
