package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 今日船舶出海查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel("今日船舶出海查询DTO")
public class TodayShipSailingQueryDTO {

    @ApiModelProperty(value = "港口片区", example = "东港片区", notes = "不传或传空表示查询所有片区")
    private String portCode;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
}
