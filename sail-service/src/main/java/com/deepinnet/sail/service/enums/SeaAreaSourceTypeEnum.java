package com.deepinnet.sail.service.enums;

import lombok.*;

/**
 * 海域来源类型枚举
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum SeaAreaSourceTypeEnum {

    /**
     * 自建海域
     */
    USER_DEFINED("user_defined", "自建海域"),

    /**
     * 高德同步的海域
     */
    GAO_DE("gao_de", "高德同步的海域"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在则返回null
     */
    public static SeaAreaSourceTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (SeaAreaSourceTypeEnum typeEnum : SeaAreaSourceTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述，如果不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        SeaAreaSourceTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getDesc() : "";
    }
}
