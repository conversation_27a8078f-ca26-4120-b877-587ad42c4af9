package com.deepinnet.sail.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.service.dto.SeaAreaFlowSummaryQueryDTO;
import com.deepinnet.sail.service.vo.SeaAreaFlowSummaryVO;

/**
 * 海域人流统计服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
public interface SeaAreaFlowSummaryService {

    /**
     * 分页查询海域人流统计数据
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<SeaAreaFlowSummaryVO> pageQuery(SeaAreaFlowSummaryQueryDTO queryDTO);
}