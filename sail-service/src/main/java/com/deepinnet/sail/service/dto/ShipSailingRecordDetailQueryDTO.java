package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR> wong
 * @create 2025/8/25 10:28
 * @Description
 *
 */
@Data
public class ShipSailingRecordDetailQueryDTO {

    @ApiModelProperty(value = "船舶编号")
    @NotBlank(message = "船舶编号不能为空")
    private String shipNo;

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "页大小不能为空")
    private Integer pageSize;
}
