package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 点位列表查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "点位列表查询DTO", description = "查询点位列表的请求参数")
public class SailPointListQueryDTO {

    @ApiModelProperty(value = "点位类型", example = "port_terminal", 
            notes = "可能值：port_terminal(港口码头), anchorage_point(锚泊点), lighthouse(灯塔), beacon(信标), monitoring_point(监控点), rescue_station(救援站), fuel_station(加油站), checkpoint(检查点), danger_point(危险点), other(其他)。为空时查询所有类型")
    private String type;

    @ApiModelProperty(value = "点位名称", example = "深圳港", notes = "支持模糊查询")
    private String name;
}

