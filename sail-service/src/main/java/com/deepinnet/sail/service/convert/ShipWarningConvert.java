package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.condition.ShipWarningQueryCondition;
import com.deepinnet.sail.dal.dto.ShipWarningDTO;
import com.deepinnet.sail.service.dto.ShipWarningQueryDTO;
import com.deepinnet.sail.service.vo.ShipWarningVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR> wong
 * @create 2025/8/22 17:27
 * @Description
 *
 */
@Mapper(componentModel = "spring")
public interface ShipWarningConvert {

    ShipWarningQueryCondition toCondition(ShipWarningQueryDTO queryDTO);

    ShipWarningVO toVO(ShipWarningDTO shipWarningDTO);

}
