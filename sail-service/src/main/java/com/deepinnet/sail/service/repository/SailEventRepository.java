package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.SailEventDO;
import com.deepinnet.sail.dal.dto.EventTypeStatisticsDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件信息Repository
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
public interface SailEventRepository extends IService<SailEventDO> {

    /**
     * 根据事件编号查询事件
     *
     * @param eventNo 事件编号
     * @return 事件信息
     */
    SailEventDO getByEventNo(String eventNo);

    /**
     * 根据事件编号删除事件
     *
     * @param eventNo 事件编号
     * @return 是否成功
     */
    Boolean removeByEventNo(String eventNo);

    /**
     * 统计各事件类型的数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 事件类型统计列表
     */
    List<EventTypeStatisticsDTO> getEventTypeStatistics(LocalDateTime startTime, LocalDateTime endTime);
}
