package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件上报DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@Data
@ApiModel(value = "事件上报DTO", description = "事件上报参数")
public class EventReportDTO {

    @ApiModelProperty(value = "事件来源", example = "tourist_rescue", notes = "必填，可选值：tourist_rescue-游客求救，traffic_event-交通事件，active_discovery-主动发现")
    @NotBlank(message = "事件来源不能为空")
    private String eventSource;

    @ApiModelProperty(value = "事件描述", example = "游客在海边遇险，需要紧急救援", notes = "必填，最大长度1000字符")
    @NotBlank(message = "事件描述不能为空")
    @Size(max = 1000, message = "事件描述最大长度为1000字符")
    private String eventDescription;

    @ApiModelProperty(value = "紧急程度", example = "urgent", notes = "必填，可选值：normal-一般，urgent-紧急，very_urgent-非常紧急")
    @NotBlank(message = "紧急程度不能为空")
    private String urgencyLevel;

    @ApiModelProperty(value = "事发地址", example = "大鹏新区南澳街道某某海滩", notes = "必填，最大长度100字符")
    @NotBlank(message = "事发地址不能为空")
    @Size(max = 100, message = "事发地址最大长度为100字符")
    private String incidentAddress;

    @ApiModelProperty(value = "经度", example = "114.123456", notes = "选填，最大长度20字符")
    @Size(max = 20, message = "经度最大长度为20字符")
    private String x;

    @ApiModelProperty(value = "纬度", example = "22.654321", notes = "选填，最大长度20字符")
    @Size(max = 20, message = "纬度最大长度为20字符")
    private String y;

    @ApiModelProperty(value = "事发时间", example = "2025-08-21T16:30:00", notes = "必填，格式：yyyy-MM-ddTHH:mm:ss")
    @NotNull(message = "事发时间不能为空")
    private LocalDateTime incidentTime;

    @ApiModelProperty(value = "上报部门", example = "南澳街道办", notes = "选填，最大长度100字符")
    @Size(max = 100, message = "上报部门最大长度为100字符")
    private String reportDepartment;

    @ApiModelProperty(value = "上报人", example = "张三", notes = "选填，最大长度20字符")
    @Size(max = 20, message = "上报人最大长度为20字符")
    private String reporterName;

    @ApiModelProperty(value = "上报人手机号", example = "13800138000", notes = "选填，最大长度11字符")
    @Size(max = 11, message = "上报人手机号最大长度为11字符")
    private String reporterPhone;

    @ApiModelProperty(value = "上报时间", example = "2025-08-21T16:35:00", notes = "必填，格式：yyyy-MM-ddTHH:mm:ss")
    @NotNull(message = "上报时间不能为空")
    private LocalDateTime reportTime;

    @ApiModelProperty(value = "附件文件地址")
    private List<String> attachments;
}
