package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 区域列表查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "区域列表查询DTO", description = "查询区域列表的请求参数")
public class SeaAreaListQueryDTO {

    @ApiModelProperty(value = "区域类型", example = "forbidden_area", 
            notes = "可能值：forbidden_area(禁海区), normal_area(普通海域), port_area(港口区域), fishing_area(渔业区域), anchorage_area(锚泊区域), channel_area(航道区域), protected_area(保护区域)。为空时查询所有类型")
    private String type;

    @ApiModelProperty(value = "区域编码", example = "AREA001", notes = "支持精确查询")
    private String areaCode;

    @ApiModelProperty(value = "区域名称", example = "深圳港", notes = "支持模糊查询")
    private String name;
}
