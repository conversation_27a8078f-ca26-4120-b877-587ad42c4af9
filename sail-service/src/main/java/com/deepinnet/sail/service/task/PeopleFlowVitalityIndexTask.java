package com.deepinnet.sail.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.service.enums.SeaAreaSourceTypeEnum;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.spatiotemporalplatform.client.sail.PeopleFlowVitalityIndexClient;
import com.deepinnet.spatiotemporalplatform.model.sail.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 人流活力指数定时任务
 * 负责定时获取和处理人流活力指数数据
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Component
public class PeopleFlowVitalityIndexTask {

    @Resource
    private PeopleFlowVitalityIndexClient peopleFlowVitalityIndexClient;

    @Resource
    private SeaAreaRepository seaAreaRepository;

    @Resource
    private PeopleFlowVitalityIndexRepository peopleFlowVitalityIndexRepository;

    /**
     * 定时获取人流活力指数数据
     * 每小时执行一次
     */
    // @Scheduled(fixedRate = 10, timeUnit = TimeUnit.SECONDS)
    @Scheduled(fixedRate = 15, timeUnit = TimeUnit.MINUTES)
    public void fetchAndProcessPeopleFlowVitalityIndex() {
        long currentTimestamp = System.currentTimeMillis();

        // 查询所有区域
        List<SeaAreaDO> seaAreaList = seaAreaRepository.list(Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, SeaAreaSourceTypeEnum.GAO_DE.getCode())
                .eq(SeaAreaDO::getIsDeleted, false));

        if (CollUtil.isEmpty(seaAreaList)) {
            LogUtil.info("系统区域表为空，无法获取人流活力指数数据");
            return;
        }

        LogUtil.info("开始执行人流活力指数定时任务，共{}个区域", seaAreaList.size());

        int processedCount = 0;
        int successCount = 0;

        for (SeaAreaDO seaAreaDO : seaAreaList) {
            try {
                // 调用接口获取人流活力指数数据
                PeopleFlowVitalityIndexDTO vitalityIndexData = fetchVitalityIndexData(seaAreaDO.getCode(), currentTimestamp);

                if (vitalityIndexData != null) {
                    // 处理获取到的数据
                    processVitalityIndexData(vitalityIndexData, seaAreaDO, currentTimestamp);
                    successCount++;
                }

                processedCount++;
            } catch (Exception e) {
                LogUtil.error("处理区域{}的人流活力指数数据失败", seaAreaDO.getCode(), e);
            }
        }

        LogUtil.info("人流活力指数定时任务执行完成，处理区域数：{}，成功数：{}", processedCount, successCount);
    }

    /**
     * 调用外部接口获取人流活力指数数据
     *
     * @param customAreaId 自定义区域id
     * @param timestamp    时间戳
     * @return 人流活力指数数据
     */
    private PeopleFlowVitalityIndexDTO fetchVitalityIndexData(String customAreaId, Long timestamp) {
        try {
            // 构建请求参数
            PeopleFlowVitalityIndexUrlParams urlParams = new PeopleFlowVitalityIndexUrlParams();
            urlParams.setCustomAreaId(customAreaId);
            urlParams.setTimestamp(timestamp);

            LogUtil.info("调用人流活力指数接口，参数：{}", JSONUtil.toJsonStr(urlParams));

            // 调用外部接口
            Result<PeopleFlowVitalityIndexDTO> result = peopleFlowVitalityIndexClient.getPeopleFlowVitalityIndex(urlParams);

            if (result == null) {
                LogUtil.error("调用人流活力指数接口失败，返回为null，区域ID：{}",
                        customAreaId);
                return null;
            }

            if (!result.isSuccess()) {
                LogUtil.error("调用人流活力指数接口失败，区域ID：{}，错误码：{}，错误描述：{}",
                        customAreaId, result.getErrorCode(), result.getErrorDesc());
                throw new BizException(result.getErrorCode(), result.getErrorDesc());
            }

            if (result.getData() == null) {
                LogUtil.info("调用人流活力指数接口成功，但返回数据为空，区域ID：{}", customAreaId);
                return null;
            }

            return result.getData();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("调用人流活力指数接口异常，区域ID：{}", customAreaId, e);
            throw new BizException(BizErrorCode.VITALITY_INDEX_API_ERROR.getCode(), BizErrorCode.VITALITY_INDEX_API_ERROR.getDesc());
        }
    }

    /**
     * 处理人流活力指数数据
     * 这里可以根据业务需求进行数据处理，比如保存到数据库、发送通知等
     *
     * @param vitalityIndexData 人流活力指数数据
     * @param seaAreaDO         区域信息
     */
    private void processVitalityIndexData(PeopleFlowVitalityIndexDTO vitalityIndexData, SeaAreaDO seaAreaDO, Long batchId) {
        try {
            PeopleFlowVitalityIndexDO indexDO = convertToVitalityIndexDO(seaAreaDO.getAreaCode(), batchId, vitalityIndexData);
            peopleFlowVitalityIndexRepository.save(indexDO);
        } catch (Exception e) {
            LogUtil.error("持久化人流活力指数失败，区域：{}", seaAreaDO.getCode(), e);
            throw e;
        }
    }

    private PeopleFlowVitalityIndexDO convertToVitalityIndexDO(String areaCode, Long batchId, PeopleFlowVitalityIndexDTO vitalityIndexData) {
        PeopleFlowVitalityIndexDO indexDO = new PeopleFlowVitalityIndexDO();
        indexDO.setAreaCode(areaCode);
        indexDO.setSeaAreaCode(vitalityIndexData.getCustomAreaId());
        indexDO.setBatchId(batchId);
        indexDO.setTime(vitalityIndexData.getTime());
        indexDO.setActivityIndex(vitalityIndexData.getActivityIndex());
        indexDO.setFlow(vitalityIndexData.getFlow());
        indexDO.setPreFlow(vitalityIndexData.getPreFlow());
        indexDO.setTodayCumulativeFlow(vitalityIndexData.getTodayCumulativeFlow());
        indexDO.setAvgFlow(vitalityIndexData.getAvgFlow());
        indexDO.setMaxFlow(vitalityIndexData.getMaxFlow());
        indexDO.setGmtCreated(LocalDateTime.now());
        indexDO.setGmtModified(LocalDateTime.now());
        return indexDO;
    }
}