package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SailPointDO;
import com.deepinnet.sail.dal.mapper.SailPointMapper;
import com.deepinnet.sail.service.repository.SailPointRepository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点位表 Repository 实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class SailPointRepositoryImpl extends ServiceImpl<SailPointMapper, SailPointDO>
        implements SailPointRepository {

    private static final String USER_DEFINED_SOURCE = "userDefined";

    @Override
    public List<SailPointDO> findUserDefinedPointsOrderByCreateTimeDesc() {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailPointDO> findUserDefinedPointsByTypeOrderByCreateTimeDesc(String type) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .eq(SailPointDO::getType, type)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailPointDO> findUserDefinedPointsByNameLikeOrderByCreateTimeDesc(String name) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .like(StringUtils.isNotBlank(name), SailPointDO::getName, name)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailPointDO> findUserDefinedPointsByTypeAndNameLikeOrderByCreateTimeDesc(String type, String name) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .eq(StringUtils.isNotBlank(type), SailPointDO::getType, type)
                .like(StringUtils.isNotBlank(name), SailPointDO::getName, name)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long excludeId) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .eq(SailPointDO::getName, name);

        if (excludeId != null) {
            wrapper.ne(SailPointDO::getId, excludeId);
        }

        return super.count(wrapper) > 0;
    }

    @Override
    public SailPointDO findUserDefinedPointById(Long id) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getId, id)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false);

        return super.getOne(wrapper);
    }

    @Override
    public List<SailPointDO> findUserDefinedPointsByTenantIdOrderByCreateTimeDesc(String tenantId) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .eq(SailPointDO::getTenantId, tenantId)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }

    @Override
    public List<SailPointDO> findUserDefinedPointsByTenantIdAndTypeOrderByCreateTimeDesc(String tenantId, String type) {
        LambdaQueryWrapper<SailPointDO> wrapper = Wrappers.lambdaQuery(SailPointDO.class)
                .eq(SailPointDO::getSource, USER_DEFINED_SOURCE)
                .eq(SailPointDO::getIsDeleted, false)
                .eq(SailPointDO::getTenantId, tenantId)
                .eq(SailPointDO::getType, type)
                .orderByDesc(SailPointDO::getGmtCreated);

        return super.list(wrapper);
    }
}

