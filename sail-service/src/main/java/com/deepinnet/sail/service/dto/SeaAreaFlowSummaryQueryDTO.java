package com.deepinnet.sail.service.dto;

import io.swagger.annotations.*;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 海域人流统计查询DTO
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Data
@ApiModel(value = "海域人流统计查询DTO", description = "查询海域人流统计的请求参数")
public class SeaAreaFlowSummaryQueryDTO {

    @ApiModelProperty(value = "区域code", example = "SEA001", notes = "为空时查询所有海域")
    private String areaCode;

    @ApiModelProperty(value = "开始日期", example = "2025-08-01", notes = "查询日期范围的开始日期，格式：yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束日期", example = "2025-08-31", notes = "查询日期范围的结束日期，格式：yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "页码", example = "1", notes = "必填，从1开始")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小", example = "10", notes = "必填，最大100")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize;
}