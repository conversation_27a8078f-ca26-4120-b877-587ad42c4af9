package com.deepinnet.sail.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 船舶出海分析VO
 *
 * <AUTHOR> wong
 * @since 2025-08-20
 */
@Data
@ApiModel("船舶出海分析VO")
public class SailingAnalysisVO {

    @ApiModelProperty("出海总次数")
    private Long totalSailingCount;

    @ApiModelProperty("出海船舶数（去重）")
    private Long uniqueShipCount;

    @ApiModelProperty("每日出海次数统计")
    private List<DailySailingCountVO> dailySailingCounts;
}
