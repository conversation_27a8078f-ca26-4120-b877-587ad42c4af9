package com.deepinnet.sail.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 区域编辑DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "区域编辑DTO", description = "编辑区域的请求参数")
public class SeaAreaUpdateDTO {

    @ApiModelProperty(value = "区域ID", example = "1", required = true)
    @NotNull(message = "区域ID不能为空")
    private Long id;

    @ApiModelProperty(value = "区域类型", example = "forbidden_area", 
            notes = "可能值：forbidden_area(禁海区), normal_area(普通海域), port_area(港口区域), fishing_area(渔业区域), anchorage_area(锚泊区域), channel_area(航道区域), protected_area(保护区域)", required = true)
    @NotBlank(message = "区域类型不能为空")
    private String type;

    @ApiModelProperty(value = "区域编码", example = "AREA001", notes = "业务层面的区域标识码，可选")
    @Size(max = 64, message = "区域编码长度不能超过64个字符")
    private String areaCode;

    @ApiModelProperty(value = "区域名称", example = "深圳港禁海区1", notes = "手动输入，最大长度：20", required = true)
    @NotBlank(message = "区域名称不能为空")
    @Size(max = 20, message = "区域名称长度不能超过20个字符")
    private String name;

    @ApiModelProperty(value = "区域面积", example = "100.5", notes = "根据右侧所绘区域范围自动计算区域面积")
    private String area;

    @ApiModelProperty(value = "区域坐标", example = "POLYGON((114.057868 22.543099, 114.064407 22.543099, 114.064407 22.547056, 114.057868 22.547056, 114.057868 22.543099))", 
            notes = "WKT格式的区域坐标信息", required = true)
    @NotBlank(message = "区域坐标不能为空")
    private String wkt;
}