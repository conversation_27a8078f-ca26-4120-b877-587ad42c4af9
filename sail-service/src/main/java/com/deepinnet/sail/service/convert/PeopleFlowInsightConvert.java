package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.SailPeopleFlowInsightDO;
import com.deepinnet.sail.service.vo.PeopleFlowInsightVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 人流洞察转换类
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Mapper(componentModel = "spring")
public interface PeopleFlowInsightConvert {

    /**
     * DO转换为VO
     *
     * @param sailPeopleFlowInsightDO 数据对象
     * @return VO对象
     */
    PeopleFlowInsightVO toVO(SailPeopleFlowInsightDO sailPeopleFlowInsightDO);

    /**
     * DO列表转换为VO列表
     *
     * @param sailPeopleFlowInsightDOList 数据对象列表
     * @return VO对象列表
     */
    List<PeopleFlowInsightVO> toVOList(List<SailPeopleFlowInsightDO> sailPeopleFlowInsightDOList);
}