package com.deepinnet.sail.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.*;

import lombok.*;

import java.time.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/22
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SailEventExportRow {
    /**
     * 事件编号
     */
    @ExcelProperty("事件编号")
    @ColumnWidth(20)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String eventNo;

    /**
     * 事件来源
     */
    @ExcelProperty("事件来源")
    @ColumnWidth(15)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String eventSource;

    /**
     * 事件描述
     */
    @ExcelProperty("事件描述")
    @ColumnWidth(30)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String eventDescription;

    /**
     * 紧急程度
     */
    @ExcelProperty("紧急程度")
    @ColumnWidth(12)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String urgencyLevel;

    /**
     * 事发地址
     */
    @ExcelProperty("事发地址")
    @ColumnWidth(25)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String incidentAddress;

    /**
     * 经度
     */
    @ExcelProperty("经度")
    @ColumnWidth(12)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String x;

    /**
     * 纬度
     */
    @ExcelProperty("纬度")
    @ColumnWidth(12)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String y;

    /**
     * 事发时间
     */
    @ExcelProperty("事发时间")
    @ColumnWidth(40)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private LocalDateTime incidentTime;

    /**
     * 上报部门
     */
    @ExcelProperty("上报部门")
    @ColumnWidth(20)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String reportDepartment;

    /**
     * 上报人
     */
    @ExcelProperty("上报人")
    @ColumnWidth(15)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String reporterName;

    /**
     * 上报人手机号
     */
    @ExcelProperty("上报人手机号")
    @ColumnWidth(15)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String reporterPhone;

    /**
     * 上报时间
     */
    @ExcelProperty("上报时间")
    @ColumnWidth(40)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private LocalDateTime reportTime;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    @ColumnWidth(12)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String status;
}
