package com.deepinnet.sail.service.convert;

import com.deepinnet.sail.dal.dataobject.SeaAreaDO;
import com.deepinnet.sail.service.dto.SeaAreaCreateDTO;
import com.deepinnet.sail.service.dto.SeaAreaDTO;
import com.deepinnet.sail.service.dto.SeaAreaUpdateDTO;
import com.deepinnet.sail.service.enums.AreaCategoryEnum;
import com.deepinnet.sail.service.vo.SeaAreaDetailVO;
import com.deepinnet.sail.service.vo.SeaAreaListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * 海域转换器
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@Mapper(componentModel = "spring")
public interface SeaAreaConvert {

    SeaAreaDTO toVO(SeaAreaDO seaAreaDO);

    List<SeaAreaDTO> toVOList(List<SeaAreaDO> seaAreaDOList);

    /**
     * DO转换为列表VO
     *
     * @param seaAreaDO DO对象
     * @return 列表VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    SeaAreaListVO toListVO(SeaAreaDO seaAreaDO);

    /**
     * DO列表转换为列表VO列表
     *
     * @param seaAreaDOList DO列表
     * @return 列表VO列表
     */
    List<SeaAreaListVO> toListVOList(List<SeaAreaDO> seaAreaDOList);

    /**
     * DO转换为详情VO
     *
     * @param seaAreaDO DO对象
     * @return 详情VO
     */
    @Mapping(source = "type", target = "typeName", qualifiedByName = "getTypeName")
    SeaAreaDetailVO toDetailVO(SeaAreaDO seaAreaDO);

    /**
     * 创建DTO转换为DO
     *
     * @param createDTO 创建DTO
     * @return DO对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    @Mapping(target = "source", constant = "userDefined")
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SeaAreaDO fromCreateDTO(SeaAreaCreateDTO createDTO);

    /**
     * 更新DTO转换为DO
     *
     * @param updateDTO 更新DTO
     * @return DO对象
     */
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "gmtCreated", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    SeaAreaDO fromUpdateDTO(SeaAreaUpdateDTO updateDTO);

    /**
     * 根据类型编码获取类型名称
     *
     * @param typeCode 类型编码
     * @return 类型名称
     */
    @Named("getTypeName")
    default String getTypeName(String typeCode) {
        return AreaCategoryEnum.getDescByCode(typeCode);
    }
}