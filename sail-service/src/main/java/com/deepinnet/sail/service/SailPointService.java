package com.deepinnet.sail.service;

import com.deepinnet.sail.service.dto.SailPointCategoryQueryDTO;
import com.deepinnet.sail.service.dto.SailPointCreateDTO;
import com.deepinnet.sail.service.dto.SailPointDetailQueryDTO;
import com.deepinnet.sail.service.dto.SailPointListQueryDTO;
import com.deepinnet.sail.service.dto.SailPointUpdateDTO;
import com.deepinnet.sail.service.vo.SailPointCategoryVO;
import com.deepinnet.sail.service.vo.SailPointDetailVO;
import com.deepinnet.sail.service.vo.SailPointListVO;

import java.util.List;

/**
 * 点位服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface SailPointService {

    /**
     * 获取点位分类列表
     *
     * @param queryDTO 查询条件
     * @return 点位分类列表
     */
    List<SailPointCategoryVO> getPointCategoryList(SailPointCategoryQueryDTO queryDTO);

    /**
     * 获取点位列表
     *
     * @param queryDTO 查询条件
     * @return 点位列表
     */
    List<SailPointListVO> getPointList(SailPointListQueryDTO queryDTO);

    /**
     * 获取点位详情
     *
     * @param queryDTO 查询条件
     * @return 点位详情
     */
    SailPointDetailVO getPointDetail(SailPointDetailQueryDTO queryDTO);

    /**
     * 创建点位
     *
     * @param createDTO 创建请求
     * @return 创建的点位ID
     */
    Long createPoint(SailPointCreateDTO createDTO);

    /**
     * 更新点位
     *
     * @param updateDTO 更新请求
     * @return 更新的点位ID
     */
    Long updatePoint(SailPointUpdateDTO updateDTO);

    /**
     * 删除点位
     *
     * @param queryDTO 删除请求
     */
    void deletePoint(SailPointDetailQueryDTO queryDTO);
}
