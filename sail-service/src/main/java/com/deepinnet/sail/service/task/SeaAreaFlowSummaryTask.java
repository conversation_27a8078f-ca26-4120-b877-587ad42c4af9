package com.deepinnet.sail.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.service.enums.SeaAreaSourceTypeEnum;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.spatiotemporalplatform.client.sail.PeopleFlowVitalityIndexClient;
import com.deepinnet.spatiotemporalplatform.model.sail.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;

/**
 * 海域每日人流统计定时任务
 * 每天23:50分执行，统计当日各海域的累计人流量
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@Component
public class SeaAreaFlowSummaryTask {

    @Resource
    private SeaAreaFlowSummaryRepository seaAreaFlowSummaryRepository;

    @Resource
    private SeaAreaRepository seaAreaRepository;

    @Resource
    private PeopleFlowVitalityIndexClient peopleFlowVitalityIndexClient;

    /**
     * 统计海域每日人流量
     * 每天23:50分执行
     */
    @Scheduled(cron = "0 50 23 * * ?")
    public void statisticsSeaAreaDailyFlow() {
        LocalDateTime today = LocalDateTime.now();
        LogUtil.info("开始执行海域每日人流统计定时任务，统计日期：{}", today.toLocalDate());

        // 幂等检查：如果今天已经有统计数据，则直接返回
        if (hasDataForToday(today)) {
            LogUtil.info("今日已存在统计数据，跳过本次执行，统计日期：{}", today.toLocalDate());
            return;
        }

        // 查询当日各海域的累计人流量
        List<SeaAreaDO> seaAreaList = seaAreaRepository.list(Wrappers.lambdaQuery(SeaAreaDO.class)
                .eq(SeaAreaDO::getSource, SeaAreaSourceTypeEnum.GAO_DE.getCode()));

        if (CollUtil.isEmpty(seaAreaList)) {
            LogUtil.info("系统区域表为空，无法获取每日人流统计数据");
            return;
        }

        Long requestTime = System.currentTimeMillis();
        List<SeaAreaFlowSummaryDO> summaryDOList = new ArrayList<>();

        for (SeaAreaDO seaAreaDO : seaAreaList) {
            PeopleFlowVitalityIndexDTO peopleFlowVitalityIndexDTO = fetchVitalityIndexData(seaAreaDO.getCode(), requestTime);
            if (peopleFlowVitalityIndexDTO == null) {
                LogUtil.info("当前海域的实时人流活力指数为空，海域code={}", seaAreaDO.getCode());
                continue;
            }

            SeaAreaFlowSummaryDO seaAreaFlowSummaryDO = buildFlowSummaryDTO(today, seaAreaDO, peopleFlowVitalityIndexDTO);
            summaryDOList.add(seaAreaFlowSummaryDO);
        }

        if (CollUtil.isNotEmpty(summaryDOList)) {
            seaAreaFlowSummaryRepository.saveBatch(summaryDOList);
        }
    }

    /**
     * 调用外部接口获取人流活力指数数据
     *
     * @param customAreaId 自定义区域id
     * @param timestamp    时间戳
     * @return 人流活力指数数据
     */
    private PeopleFlowVitalityIndexDTO fetchVitalityIndexData(String customAreaId, Long timestamp) {
        try {
            // 构建请求参数
            PeopleFlowVitalityIndexUrlParams urlParams = new PeopleFlowVitalityIndexUrlParams();
            urlParams.setCustomAreaId(customAreaId);
            urlParams.setTimestamp(timestamp);

            LogUtil.info("调用人流活力指数接口，参数：{}", JSONUtil.toJsonStr(urlParams));

            // 调用外部接口
            Result<PeopleFlowVitalityIndexDTO> result = peopleFlowVitalityIndexClient.getPeopleFlowVitalityIndex(urlParams);

            if (result == null) {
                LogUtil.error("调用人流活力指数接口失败，返回为null，区域ID：{}",
                        customAreaId);
                return null;
            }

            if (!result.isSuccess()) {
                LogUtil.error("调用人流活力指数接口失败，区域ID：{}，错误码：{}，错误描述：{}",
                        customAreaId, result.getErrorCode(), result.getErrorDesc());
                throw new BizException(result.getErrorCode(), result.getErrorDesc());
            }

            if (result.getData() == null) {
                LogUtil.info("调用人流活力指数接口成功，但返回数据为空，区域ID：{}", customAreaId);
                return null;
            }

            return result.getData();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LogUtil.error("调用人流活力指数接口异常，区域ID：{}", customAreaId, e);
            throw new BizException(BizErrorCode.VITALITY_INDEX_API_ERROR.getCode(), BizErrorCode.VITALITY_INDEX_API_ERROR.getDesc());
        }
    }

    private SeaAreaFlowSummaryDO buildFlowSummaryDTO(LocalDateTime now, SeaAreaDO seaAreaDO, PeopleFlowVitalityIndexDTO peopleFlowVitalityIndexDTO) {
        SeaAreaFlowSummaryDO seaAreaFlowSummaryDO = new SeaAreaFlowSummaryDO();
        seaAreaFlowSummaryDO.setSeaAreaCode(seaAreaDO.getCode());
        seaAreaFlowSummaryDO.setSeaAreaName(seaAreaDO.getName());
        seaAreaFlowSummaryDO.setSummaryDate(now);
        seaAreaFlowSummaryDO.setTodayTotalFlow(peopleFlowVitalityIndexDTO.getTodayCumulativeFlow());
        seaAreaFlowSummaryDO.setGmtCreated(LocalDateTime.now());
        seaAreaFlowSummaryDO.setGmtModified(LocalDateTime.now());
        return seaAreaFlowSummaryDO;
    }

    /**
     * 检查今日是否已经有统计数据
     *
     * @param dateTime 统计日期时间
     * @return true-已存在数据，false-不存在数据
     */
    private boolean hasDataForToday(LocalDateTime dateTime) {
        LocalDate date = dateTime.toLocalDate();
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59, 999999999);
        
        long count = seaAreaFlowSummaryRepository.count(Wrappers.lambdaQuery(SeaAreaFlowSummaryDO.class)
                .between(SeaAreaFlowSummaryDO::getSummaryDate, startOfDay, endOfDay)
                .eq(SeaAreaFlowSummaryDO::getIsDeleted, false));
        return count > 0;
    }
}