package com.deepinnet.sail.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;

import java.util.List;

/**
 * 船舶出海记录服务接口
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
public interface ShipSailingRecordService {

    /**
     * 管理后台-分页查询出海记录列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<SailingRecordVO> pageSailingRecords(SailingRecordQueryDTO queryDTO);

    /**
     * 查询出海记录轨迹接口
     *
     * @param queryDTO
     * @return
     */
    List<ShipSailingRecordTraceDTO> getShipSailingRecordTrace(ShipSailingRecordTraceQueryDTO queryDTO);

    /**
     * 获取船舶的全部出海记录
     *
     * @param queryDTO
     * @return
     */
    CommonPage<ShipSailingRecordDetailDTO> pageShipSailingRecordDetails(ShipSailingRecordDetailQueryDTO queryDTO);

    /**
     * 船舶出海分析
     *
     * @param queryDTO 查询条件
     * @return 分析结果
     */
    SailingAnalysisVO getSailingAnalysis(SailingAnalysisQueryDTO queryDTO);


    /**
     * 各港口出海次数排行
     *
     * @param queryDTO
     * @return
     */
    List<PortAreaSailingRankVO> getPortSailingRank(SailingAnalysisQueryDTO queryDTO);

    /**
     * 今日船舶出海
     *
     * @param queryDTO 查询条件
     * @return 今日船舶出海结果
     */
    TodayShipSailingVO getTodayShipSailing(TodayShipSailingQueryDTO queryDTO);
}
