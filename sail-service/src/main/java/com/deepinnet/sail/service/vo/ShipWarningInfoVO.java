package com.deepinnet.sail.service.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警信息VO
 */
@Data
@ApiModel(value = "预警信息VO")
public class ShipWarningInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    private Long id;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "预警描述")
    private String description;

    @ApiModelProperty(value = "触发时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm")
    private LocalDateTime triggerTime;
}