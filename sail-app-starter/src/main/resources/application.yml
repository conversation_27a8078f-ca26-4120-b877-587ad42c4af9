server:
  port: 8080

spring:
  application:
    name: sail
  servlet:
    multipart:
      # 文件上传的最大大小
      max-file-size: 50MB
      # 请求的最大大小（包含表单数据）
      max-request-size: 250MB

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  sql:
#    print: true
  type-handlers-package: com.deepinnet.sail.dal.typehandler
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: true
      logic-not-delete-value: false

pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

knife4j:
  enable: true
  openapi:
    title: 船舶系统
    description: 船舶系统
    version: v4.0
    group:
      web:
        group-name: web
        api-rule: package
        api-rule-resources:
          - com.deepinnet

feign:
  httpclient:
    enabled: true
  okhttp:
    enabled: false

salt: shendu188


sa-token:
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从请求体里读取token
  is-read-body: true
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: true
  # token风格
  token-style: jwt
  # jwt秘钥
  jwt-secret-key: shendu188
  timeout: 17280000
