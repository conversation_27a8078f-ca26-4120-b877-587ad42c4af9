package com.deepinnet.sail.config;

import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wong
 * @create 2025/2/14 11:15
 * @Description
 */
@Component
public class SaTokenJwtConfig {
    /**
     * Sa-Token 整合 jwt (服务器完全无状态)
     *
     * @return
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
        //return new StpLogicJwtForStateless();
    }
}
