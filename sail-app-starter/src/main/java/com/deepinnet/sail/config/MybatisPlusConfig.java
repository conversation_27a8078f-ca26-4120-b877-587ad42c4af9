package com.deepinnet.sail.config;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.*;
import org.springframework.context.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * MyBatis-Plus 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {

//    @Resource
//    private DeepinnetTenantLineHandler tenantLineHandler;

    /**
     * 配置分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        // 注册租户行拦截器
        // interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(tenantLineHandler));
        return interceptor;
    }

} 