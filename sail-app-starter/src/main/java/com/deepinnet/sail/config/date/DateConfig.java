package com.deepinnet.sail.config.date;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.*;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.*;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigInteger;
import java.text.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**

 * @since 2024-08-07 星期三
 **/
@Configuration
public class DateConfig {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Component
    public static class StringToDateConvert implements Converter<String, Date> {
        @Override
        public Date convert(String source) {
            Date date;
            try {
                long timestamp = Long.parseLong(source);
                date = new Date(timestamp);
            } catch (NumberFormatException e) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    date = sdf.parse(source);
                } catch (ParseException e1) {
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    try {
                        date = sdf2.parse(source);
                    } catch (ParseException e2) {
                        SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd");
                        try {
                            date = sdf3.parse(source);
                        } catch (ParseException e3) {
                            throw new IllegalArgumentException(String.format("日期格式转换出错，值:[%s]", source), e3);
                        }
                    }
                }
            }
            return date;
        }
    }

    /**
     * 入参-形参、@RequestParam和@PathVariable时候-字符串转Date相关类
     */
    @Component
    public static class LocalDateConvert implements Converter<String, LocalDate> {
        @Override
        public LocalDate convert(String timestamp) {
            if (StrUtil.isEmpty(timestamp)) {
                return null;
            }

            return LocalDate.parse(timestamp.trim(), DATE_FORMATTER);
        }
    }

    /**
     * 入参-形参、@RequestParam和@PathVariable时候-字符串转Date相关类
     */
    @Component
    public static class LocalDateTimeConvert implements Converter<String, LocalDateTime> {
        @Override
        public LocalDateTime convert(String timestamp) {
            if (StrUtil.isEmpty(timestamp)) {
                return null;
            }
            try {
                return LocalDateTime.parse(timestamp.trim());
            }catch (Exception e) {
                return LocalDateTimeUtil.of(Long.parseLong(timestamp), TimeZone.getDefault());
            }
        }
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.serializerByType(LocalDateTime.class, new LocalDateTimeSerializer());
            builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer());

            builder.serializerByType(LocalDate.class, new LocalDateSerializer(DATE_FORMATTER));
            builder.deserializerByType(LocalDate.class, new LocalDateDeserializer(DATE_FORMATTER));

            builder.serializerByType(LocalTime.class, new LocalTimeSerializer(TIME_FORMATTER));
            builder.deserializerByType(LocalTime.class, new LocalTimeDeserializer(TIME_FORMATTER));


            builder.serializerByType(Date.class, new DateSerializer());
            builder.deserializerByType(Date.class, new DateDeserializer());

            //避免丢失精度
            builder.serializerByType(LocalDate.class, ToStringSerializer.instance);
            builder.serializerByType(Long.TYPE, ToStringSerializer.instance);
            builder.serializerByType(BigInteger.class, ToStringSerializer.instance);
        };
    }

    public static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            if (value != null) {
                gen.writeNumber(value.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            }
        }
    }

    public static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser parser, DeserializationContext deserializationContext) throws IOException {
            try {
                return LocalDateTime.parse(parser.getText().trim());
            } catch (Exception e) {
                long timestamp = parser.getValueAsLong();
                return timestamp < 0 ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            }
        }
    }

    public static class DateSerializer extends JsonSerializer<Date> {
        @Override
        public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            if (value != null) {
                gen.writeNumber(value.getTime());
            }
        }
    }

    public static class DateDeserializer extends JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonParser parser, DeserializationContext deserializationContext) throws IOException {
            String source = parser.getText().trim();
            Date date;
            try {
                long timestamp = Long.parseLong(source);
                date = new Date(timestamp);
            } catch (NumberFormatException e) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    date = sdf.parse(source);
                } catch (ParseException e1) {
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    try {
                        date = sdf2.parse(source);
                    } catch (ParseException e2) {
                        SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd");
                        try {
                            date = sdf3.parse(source);
                        } catch (ParseException e3) {
                            throw new IllegalArgumentException(String.format("日期格式转换出错，字段名:[%s]，值:[%s]", parser.getCurrentName(), source), e3);
                        }
                    }
                }
            }
            return date;
        }
    }
}
