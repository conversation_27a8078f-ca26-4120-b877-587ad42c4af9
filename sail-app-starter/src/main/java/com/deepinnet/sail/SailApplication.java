package com.deepinnet.sail;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> wong
 * @create 2025/8/20 11:26
 * @Description
 *
 */
@EnableScheduling
@SpringBootApplication
@ComponentScan("com.deepinnet")
@MapperScan({"com.deepinnet.sail.dal.mapper", "com.deepinnet.lock.mapper"})
@EnableFeignClients(basePackages = "com.deepinnet")
@EnableAspectJAutoProxy
public class SailApplication {

    public static void main(String[] args) {
        SpringApplication.run(SailApplication.class, args);
    }
}
