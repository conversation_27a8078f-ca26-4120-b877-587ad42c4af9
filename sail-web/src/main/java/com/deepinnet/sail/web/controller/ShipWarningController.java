package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.ShipWarningService;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 船舶预警记录控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-22
 */
@RestController
@RequestMapping("/api/sail/warning")
@Api(tags = "船舶预警记录管理")
@RequiredArgsConstructor
public class ShipWarningController {

    private final ShipWarningService shipWarningService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询船舶预警记录", notes = "展示所有休闲船舶预警记录列表")
    public Result<CommonPage<ShipWarningVO>> pageShipWarnings(@Valid @RequestBody ShipWarningQueryDTO queryDTO) {
        return Result.success(shipWarningService.pageShipWarnings(queryDTO));
    }

    @PostMapping("/dashboard/summary")
    @ApiOperation(value = "获取大屏预警总览数据", notes = "获取大屏预警统计和总数据，预警类型可能值：RISK_TIME_SAILING(风险时段出海), SEA_AREA_DEVIATION(海域偏离), FORBIDDEN_AREA(前往禁海区), OVERDUE_RETURN(到时未归), DAILY_NOT_RETURN(当日未归), REMOTE_NOT_RETURN(异地未归)")
    public Result<WarningDashboardSummaryVO> getWarningDashboardSummary(@Valid @RequestBody WarningDashboardSummaryQueryDTO queryDTO) {
        return Result.success(shipWarningService.getWarningDashboardSummary(queryDTO));
    }
}
