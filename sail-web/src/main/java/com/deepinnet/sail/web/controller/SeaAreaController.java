package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.SeaAreaService;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 海域管理控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@RestController
@RequestMapping("/api/sail/area")
@Api(tags = "海域管理")
@RequiredArgsConstructor
@Validated
public class SeaAreaController {

    private final SeaAreaService seaAreaService;

    @PostMapping("/list")
    @ApiOperation(value = "根据类型查询海域/港口", 
            notes = "根据类型查询海域/港口列表，类型可能值：forbidden(禁海区), normal(普通海域), port(港口)")
    public Result<List<SeaAreaDTO>> getSeaAreasByType(@Valid @RequestBody SeaAreaQueryDTO queryDTO) {
        return Result.success(seaAreaService.getSeaAreasByType(queryDTO));
    }

    @PostMapping("/categories")
    @ApiOperation(value = "获取区域分类列表", 
            notes = "获取所有区域分类及每个分类下的区域列表，按创建时间倒序排序")
    public Result<List<SeaAreaCategoryVO>> getAreaCategoryList(@Valid @RequestBody SeaAreaCategoryQueryDTO queryDTO) {
        return Result.success(seaAreaService.getAreaCategoryList(queryDTO));
    }

    @PostMapping("/query")
    @ApiOperation(value = "根据条件查询区域列表", 
            notes = "支持按分类和名称查询区域列表，按创建时间倒序排序")
    public Result<List<SeaAreaListVO>> getAreaList(@Valid @RequestBody SeaAreaListQueryDTO queryDTO) {
        return Result.success(seaAreaService.getAreaList(queryDTO));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "根据ID获取区域详情")
    public Result<SeaAreaDetailVO> getAreaDetail(@Valid @RequestBody SeaAreaDetailQueryDTO queryDTO) {
        return Result.success(seaAreaService.getAreaDetail(queryDTO));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增区域", 
            notes = "新增区域时会自动生成区域编码，并验证区域名称唯一性")
    public Result<Long> createArea(@Valid @RequestBody SeaAreaCreateDTO createDTO) {
        return Result.success(seaAreaService.createArea(createDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑区域", 
            notes = "编辑区域时会验证区域名称唯一性（排除当前区域）")
    public Result<Boolean> updateArea(@Valid @RequestBody SeaAreaUpdateDTO updateDTO) {
        return Result.success(seaAreaService.updateArea(updateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除区域", 
            notes = "删除区域（逻辑删除）")
    public Result<Boolean> deleteArea(@Valid @RequestBody SeaAreaDetailQueryDTO queryDTO) {
        return Result.success(seaAreaService.deleteArea(queryDTO.getId()));
    }
}