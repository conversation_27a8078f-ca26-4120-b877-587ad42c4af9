package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.PeopleFlowInsightService;
import com.deepinnet.sail.service.dto.PeopleFlowInsightQueryDTO;
import com.deepinnet.sail.service.vo.PeopleFlowInsightVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 人流洞察控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-27
 */
@Api(tags = "人流洞察管理")
@RestController
@RequestMapping("/sail/people")
@RequiredArgsConstructor
public class PeopleFlowInsightController {

    private final PeopleFlowInsightService peopleFlowInsightService;

    @ApiOperation(value = "查询最新人流洞察数据", notes = "根据洞察维度查询最新时间的人流洞察数据")
    @PostMapping("/insight")
    public Result<List<PeopleFlowInsightVO>> queryLatestInsightData(@Valid @RequestBody PeopleFlowInsightQueryDTO queryDTO) {
        List<PeopleFlowInsightVO> result = peopleFlowInsightService.queryLatestInsightData(queryDTO);
        return Result.success(result);
    }
}