package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.SeaAreaFlowSummaryService;
import com.deepinnet.sail.service.dto.SeaAreaFlowSummaryQueryDTO;
import com.deepinnet.sail.service.vo.SeaAreaFlowSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 海域人流统计控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@RestController
@RequestMapping("/api/sail/people")
@Api(tags = "海域人流统计管理")
@RequiredArgsConstructor
@Validated
public class SeaAreaFlowSummaryController {

    private final SeaAreaFlowSummaryService seaAreaFlowSummaryService;

    @PostMapping("/flow/summary")
    @ApiOperation(value = "管理后台-海上活动人数")
    public Result<CommonPage<SeaAreaFlowSummaryVO>> pageQuery(@Valid @RequestBody SeaAreaFlowSummaryQueryDTO queryDTO) {
        return Result.success(seaAreaFlowSummaryService.pageQuery(queryDTO));
    }
}