package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.SailRouteService;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 线路管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/api/sail/route")
@Api(tags = "线路管理")
@RequiredArgsConstructor
@Validated
public class SailRouteController {

    private final SailRouteService sailRouteService;

    @PostMapping("/categories")
    @ApiOperation(value = "获取线路分类列表", 
            notes = "获取所有线路分类及每个分类下的线路列表，按创建时间倒序排序")
    public Result<List<SailRouteCategoryVO>> getRouteCategoryList(@Valid @RequestBody SailRouteCategoryQueryDTO queryDTO) {
        return Result.success(sailRouteService.getRouteCategoryList(queryDTO));
    }

    @PostMapping("/query")
    @ApiOperation(value = "根据条件查询线路列表", 
            notes = "支持按分类和名称查询线路列表，按创建时间倒序排序")
    public Result<List<SailRouteListVO>> getRouteList(@Valid @RequestBody SailRouteListQueryDTO queryDTO) {
        return Result.success(sailRouteService.getRouteList(queryDTO));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "根据ID获取线路详情")
    public Result<SailRouteDetailVO> getRouteDetail(@Valid @RequestBody SailRouteDetailQueryDTO queryDTO) {
        return Result.success(sailRouteService.getRouteDetail(queryDTO));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增线路", 
            notes = "新增线路时会自动生成线路编码，并验证线路名称唯一性")
    public Result<Long> createRoute(@Valid @RequestBody SailRouteCreateDTO createDTO) {
        return Result.success(sailRouteService.createRoute(createDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑线路", 
            notes = "编辑线路时会验证线路名称唯一性（排除当前线路）")
    public Result<Boolean> updateRoute(@Valid @RequestBody SailRouteUpdateDTO updateDTO) {
        return Result.success(sailRouteService.updateRoute(updateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除线路", 
            notes = "删除线路（逻辑删除）")
    public Result<Boolean> deleteRoute(@Valid @RequestBody SailRouteDetailQueryDTO queryDTO) {
        return Result.success(sailRouteService.deleteRoute(queryDTO.getId()));
    }
}
