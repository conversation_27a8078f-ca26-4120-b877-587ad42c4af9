package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.SailEventService;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 事件管理Controller
 *
 * <AUTHOR> wong
 * @since 2025-08-21
 */
@RestController
@RequestMapping("/api/sail/events")
@RequiredArgsConstructor
@Validated
@Api(tags = "事件服务")
public class SailEventController {

    private final SailEventService sailEventService;

    @PostMapping("/page")
    @ApiOperation(value = "管理后台分页查询事件列表")
    public Result<CommonPage<EventVO>> pageEvents(@Valid @RequestBody EventQueryDTO queryDTO) {
        return Result.success(sailEventService.pageEvents(queryDTO));
    }

    @PostMapping("/dashboard/page")
    @ApiOperation(value = "获取大屏事件完整数据（统计+列表）")
    public Result<EventDashboardVO> pageQueryDashboardEvents(@Valid @RequestBody EventDashboardQueryDTO requestDTO) {
        return Result.success(sailEventService.pageQueryDashboardEvents(requestDTO));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "根据事件编号查询事件详情")
    public Result<EventVO> getEventDetail(@Valid @RequestBody EventDetailQueryDTO queryDTO) {
        return Result.success(sailEventService.getEventDetail(queryDTO.getEventNo()));
    }

    @PostMapping("/report")
    @ApiOperation(value = "上报事件")
    public Result<String> reportEvent(@Valid @RequestBody EventReportDTO reportDTO) {
        return Result.success(sailEventService.reportEvent(reportDTO));
    }

    @PostMapping("/process")
    @ApiOperation(value = "处理事件")
    public Result<Long> processEvent(@Valid @RequestBody EventProcessDTO processDTO) {
        return Result.success(sailEventService.processEvent(processDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除事件")
    public Result<Boolean> deleteEvent(@Valid @RequestBody EventDeleteDTO deleteDTO) {
        return Result.success(sailEventService.deleteEvent(deleteDTO.getEventNo()));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出事件列表")
    public void pageEvents(@Valid @RequestBody EventQueryDTO queryDTO, HttpServletResponse response) {
        sailEventService.exportEvents(queryDTO, response);
    }
}
