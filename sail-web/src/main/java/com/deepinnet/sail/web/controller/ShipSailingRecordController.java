package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.ShipSailingRecordService;
import com.deepinnet.sail.service.vo.*;

import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 船舶出海记录管理控制器
 * </p>
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@RestController
@RequestMapping("/api/sail/sailing/record")
@Api(tags = "船舶出海记录管理")
public class ShipSailingRecordController {

    @Resource
    private ShipSailingRecordService shipSailingRecordService;

    @PostMapping("/page")
    @ApiOperation(value = "大屏&管理后台-分页查询出海记录")
    public Result<CommonPage<SailingRecordVO>> pageSailingRecords(@RequestBody @Valid SailingRecordQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.pageSailingRecords(queryDTO));
    }

    @PostMapping("/page/detail")
    @ApiOperation(value = "获取某个船舶的出海记录")
    public Result<CommonPage<ShipSailingRecordDetailDTO>> pageShipSailingRecordDetails(@RequestBody @Valid ShipSailingRecordDetailQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.pageShipSailingRecordDetails(queryDTO));
    }

    @PostMapping("/trace")
    @ApiOperation(value = "查询出海记录轨迹")
    public Result<List<ShipSailingRecordTraceDTO>> getShipSailingRecordTrace(@RequestBody @Valid ShipSailingRecordTraceQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getShipSailingRecordTrace(queryDTO));
    }

    @PostMapping("/analysis")
    @ApiOperation(value = "船舶出海分析", notes = "展示指定时间范围内的出海统计分析数据")
    public Result<SailingAnalysisVO> getSailingAnalysis(@RequestBody @Valid SailingAnalysisQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @PostMapping("/analysis/rank")
    @ApiOperation(value = "船舶出海分析-排行", notes = "展示指定时间范围内的出海排行数据")
    public Result<List<PortAreaSailingRankVO>> getPortSailingRank(@RequestBody @Valid SailingAnalysisQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getPortSailingRank(queryDTO));
    }


    @PostMapping("/today")
    @ApiOperation(value = "今日船舶出海", notes = "展示今日船舶出海情况，包括统计数据和出海记录列表")
    public Result<TodayShipSailingVO> getTodayShipSailing(@RequestBody TodayShipSailingQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getTodayShipSailing(queryDTO));
    }
}
