package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.ShipService;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.vo.ShipDetailVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 船舶管理控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-23
 */
@RestController
@RequestMapping("/api/sail/ship")
@Api(tags = "船舶管理")
@RequiredArgsConstructor
public class ShipController {

    private final ShipService shipService;

    @PostMapping("/list")
    @ApiOperation(value = "大屏查询船舶以及最新位置",
            notes = "查询所有船舶的详细信息，船舶类型可能值：operation_management(运管), self_use(自用)")
    public Result<List<ShipDetailVO>> getAllShipDetail(@Valid @RequestBody ShipDetailQueryDTO queryDTO) {
        return Result.success(shipService.getAllShipDetail(queryDTO));
    }

    @PostMapping("/position")
    @ApiOperation(value = "查询船舶实时位置", 
            notes = "查询船舶的1小时内最新位置信息，不传入shipNos时查询所有船舶")
    public Result<List<ShipPositionDTO>> getAllShipPosition(@Valid @RequestBody ShipPositionQueryDTO queryDTO) {
        return Result.success(shipService.getAllShipLatestPosition(queryDTO));
    }
}