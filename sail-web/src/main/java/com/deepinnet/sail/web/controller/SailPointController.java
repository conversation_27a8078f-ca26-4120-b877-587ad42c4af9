package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.SailPointService;
import com.deepinnet.sail.service.dto.SailPointCategoryQueryDTO;
import com.deepinnet.sail.service.dto.SailPointCreateDTO;
import com.deepinnet.sail.service.dto.SailPointDetailQueryDTO;
import com.deepinnet.sail.service.dto.SailPointListQueryDTO;
import com.deepinnet.sail.service.dto.SailPointUpdateDTO;
import com.deepinnet.sail.service.vo.SailPointCategoryVO;
import com.deepinnet.sail.service.vo.SailPointDetailVO;
import com.deepinnet.sail.service.vo.SailPointListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 点位管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Api(tags = "点位管理")
@RestController
@RequestMapping("/point")
public class SailPointController {

    @Autowired
    private SailPointService sailPointService;

    /**
     * 获取点位分类列表
     *
     * @param queryDTO 查询条件
     * @return 点位分类列表
     */
    @ApiOperation("获取点位分类列表")
    @PostMapping("/categories")
    public Result<List<SailPointCategoryVO>> getPointCategoryList(@RequestBody @Valid SailPointCategoryQueryDTO queryDTO) {
        List<SailPointCategoryVO> result = sailPointService.getPointCategoryList(queryDTO);
        return Result.success(result);
    }

    /**
     * 获取点位列表
     *
     * @param queryDTO 查询条件
     * @return 点位列表
     */
    @ApiOperation("获取点位列表")
    @PostMapping("/list")
    public Result<List<SailPointListVO>> getPointList(@RequestBody @Valid SailPointListQueryDTO queryDTO) {
        List<SailPointListVO> result = sailPointService.getPointList(queryDTO);
        return Result.success(result);
    }

    /**
     * 获取点位详情
     *
     * @param queryDTO 查询条件
     * @return 点位详情
     */
    @ApiOperation("获取点位详情")
    @PostMapping("/detail")
    public Result<SailPointDetailVO> getPointDetail(@RequestBody @Valid SailPointDetailQueryDTO queryDTO) {
        SailPointDetailVO result = sailPointService.getPointDetail(queryDTO);
        return Result.success(result);
    }

    /**
     * 创建点位
     *
     * @param createDTO 创建请求
     * @return 创建的点位ID
     */
    @ApiOperation("创建点位")
    @PostMapping("/create")
    public Result<Long> createPoint(@RequestBody @Valid SailPointCreateDTO createDTO) {
        Long pointId = sailPointService.createPoint(createDTO);
        return Result.success(pointId);
    }

    /**
     * 更新点位
     *
     * @param updateDTO 更新请求
     * @return 更新的点位ID
     */
    @ApiOperation("更新点位")
    @PostMapping("/update")
    public Result<Long> updatePoint(@RequestBody @Valid SailPointUpdateDTO updateDTO) {
        Long pointId = sailPointService.updatePoint(updateDTO);
        return Result.success(pointId);
    }

    /**
     * 删除点位
     *
     * @param queryDTO 删除请求
     * @return 删除结果
     */
    @ApiOperation("删除点位")
    @PostMapping("/delete")
    public Result<Void> deletePoint(@RequestBody @Valid SailPointDetailQueryDTO queryDTO) {
        sailPointService.deletePoint(queryDTO);
        return Result.success(null);
    }
}
