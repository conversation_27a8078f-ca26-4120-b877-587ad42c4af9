package com.deepinnet.sail.web.controller;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.PeopleFlowVitalityIndexService;
import com.deepinnet.sail.service.dto.PeopleFlowVitalityIndexQueryDTO;
import com.deepinnet.sail.service.vo.PeopleFlowVitalityIndexVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 人流活力指数控制器
 *
 * <AUTHOR> wong
 * @since 2025-08-29
 */
@RestController
@RequestMapping("/api/sail/people")
@Api(tags = "人流活力指数管理")
@RequiredArgsConstructor
@Validated
public class PeopleFlowVitalityIndexController {

    private final PeopleFlowVitalityIndexService peopleFlowVitalityIndexService;

    @PostMapping("/vitality/latest")
    @ApiOperation(value = "查询最新批次人流活力指数", 
            notes = "根据区域code查询最新批次的人流活力指数数据，返回该批次的所有数据")
    public Result<PeopleFlowVitalityIndexVO> getLatestByAreaCode(@Valid @RequestBody PeopleFlowVitalityIndexQueryDTO queryDTO) {
        return Result.success(peopleFlowVitalityIndexService.getLatestByAreaCode(queryDTO));
    }
}