package com.deepinnet.sail.common.context;

public class TenantContext {
    private static final ThreadLocal<Boolean> DISABLE_TENANT = new ThreadLocal<>();

    /**
     * 禁用租户id功能
     * 慎用：只在定时任务或者消息中使用该标记位，并且需要保证不通过租户id能唯一定位到业务数据
     * 不然会出现水平越权访问其他租户数据的问题
     */
    public static void disableTenantLine() {
        DISABLE_TENANT.set(true);
    }

    public static boolean isTenantLineDisabled() {
        return Boolean.TRUE.equals(DISABLE_TENANT.get());
    }

    public static void clear() {
        DISABLE_TENANT.remove();
    }
}