package com.deepinnet.sail.common.util;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR> wong
 * @create 2025/8/21 17:57
 * @Description
 *
 */
public class BizNoGenerateUtil {

    public static String generateEventNo() {
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        return "event_" +  snowflakeNextIdStr;
    }

    public static String generateSeaAreaCode() {
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        return "SA" + snowflakeNextIdStr;
    }

    public static String generateSailRouteCode() {
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        return "SR" + snowflakeNextIdStr;
    }

    public static String generateSailPointCode() {
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        return "SP" + snowflakeNextIdStr;
    }
}
