package com.deepinnet.sail.common.error;

import lombok.Getter;

/**
 * 业务错误码
 *
 * <AUTHOR>
 */
@Getter
public enum BizErrorCode {


    /**
     * 通用错误码
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统内部错误"),
    ILLEGAL_PARAMS("IL<PERSON><PERSON>L_PARAMS", "参数非法"),
    ILLEGAL_REQUEST("ILLEGAL_REQUEST", "非法请求"),
    OPERATION_FAILED("OPERATION_FAILED", "操作失败"),
    NOT_FOUND("NOT_FOUND", "未找到相关资源"),
    ALREADY_EXISTS("ALREADY_EXISTS", "资源已存在"),
    UNKNOWN_EXCEPTION("UNKNOWN_EXCEPTION", "未知异常"),
    NOT_EXIST_ERROR("NOT_EXIST_ERROR", "暂无数据"),
    FILE_DOWNLOAD_ERROR("FILE_DOWNLOAD_ERROR", "文件下载异常"),
    NO_PERMISSION("NO_PERMISSION", "无权限"),


    ACCOUNT_LOGIN_EXPIRE("account_login_expire", "当前账号登录已经失效，请重新登录"),

    ACCOUNT_NOT_LOGIN("account_not_login", "当前账号未登录，请先登录"),

    FILE_SIZE_EXCEEDS_THE_LIMIT("file_size_exceeds_the_limit", "文件大小超过限制"),

    LENGTH_EXCEEDS_LIMIT("length_exceeds_limit", "长度超过限制"),

    /**
     * 船舶
     */
    PARSE_SHIP_TIME_CONFIG_ERROR("parse_ship_time_config_error", "解析船舶时段配置失败"),

    PORT_NOT_FOUND("port_not_found", "港口不存在"),

    SAILING_RECORD_NOT_FOUND("sailing_record_not_found", "出海记录不存在"),

    EVENT_NOT_FOUND("event_not_found", "事件不存在"),

    PROCESS_INVALID_EVENT_STATUS("process_invalid_event_status", "只有待处理和处理中状态的事件才能进行处理"),

    PROCESS_INVALID_PROCESS_STAGE("process_invalid_process_stage", "处理阶段非法"),

    /**
     * 区域管理
     */
    AREA_NAME_ALREADY_EXISTS("area_name_already_exists", "该区域已存在！"),
    
    AREA_NOT_FOUND("area_not_found", "区域不存在"),
    
    AREA_TYPE_INVALID("area_type_invalid", "区域类型无效"),
    
    AREA_COORDINATES_INVALID("area_coordinates_invalid", "区域坐标无效"),
    
    AREA_CODE_ALREADY_EXISTS("area_code_already_exists", "该区域编码已存在！"),

    /**
     * 线路管理
     */
    ROUTE_NAME_ALREADY_EXISTS("route_name_already_exists", "该线路已存在！"),
    
    ROUTE_NOT_FOUND("route_not_found", "线路不存在"),
    
    ROUTE_TYPE_INVALID("route_type_invalid", "线路类型无效"),
    
    ROUTE_COORDINATES_INVALID("route_coordinates_invalid", "线路坐标无效"),

    /**
     * 点位管理
     */
    POINT_NAME_ALREADY_EXISTS("point_name_already_exists", "该点位已存在！"),
    
    POINT_NOT_FOUND("point_not_found", "点位不存在"),
    
    POINT_TYPE_INVALID("point_type_invalid", "点位类型无效"),
    
    POINT_COORDINATES_INVALID("point_coordinates_invalid", "点位坐标无效"),

    /**
     * 高德API
     */
    VITALITY_INDEX_API_ERROR("vitality_index_api_error", "调用人流活力指数接口失败")
    ;

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String desc;

    BizErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}