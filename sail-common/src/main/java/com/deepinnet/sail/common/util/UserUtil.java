package com.deepinnet.sail.common.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 16:14
 * @Description
 */
public class UserUtil {

    public static String getUserNo() {
        return (String) StpUtil.getExtra("userNo");
    }

    public static String getUserName() {
        return (String) StpUtil.getExtra("name");
    }

    public static String getAccount() {
        return (String) StpUtil.getExtra("account");
    }

    @SuppressWarnings("unchecked")
    public static List<Long> getDepartmentIds() {
        List<?> rawList = (List<?>) StpUtil.getExtra("departmentIds");
        if (CollUtil.isEmpty(rawList)) {
            return Collections.emptyList();
        }

        return rawList.stream()
                .map(obj -> {
                    if (obj instanceof Integer) {
                        return ((Integer) obj).longValue();
                    } else if (obj instanceof Long) {
                        return (Long) obj;
                    } else {
                        return Long.valueOf(obj.toString());
                    }
                })
                .collect(Collectors.toList());
    }
}
